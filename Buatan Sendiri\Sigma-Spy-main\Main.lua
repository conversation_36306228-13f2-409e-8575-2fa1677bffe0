--[[
	⣿⣿⣿⣿⣿ SIGMA SPY ⣿⣿⣿⣿⣿
	⣿⣿⣯⡉⠉⠉⠉⠉⠉⠉⠉⠉⠉⠉⠉⠉⠉⠉
	⠉⠻⣿⣿⣦⣀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠈⠻⣿⣿⣷⣄⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠙⢿⣿⣿⣦⡀⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⠀⠀⣉⣿⣿⣿⠆⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⠀⠀⠀⣠⣾⣿⣿⠟⠁⠀⠀⠀⠀⠀⠀⠀⠀
	⠀⠀⢀⣴⣿⣿⡿⠋⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⣀⣴⣿⣿⠟⠉⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀
	⣿⣿⣟⣁⣀⣀⣀⣀⣀⣀⣀⣀⣀⣀⣀⣀⣀⣀
	⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿

	<AUTHOR> (depthso)
	@repo https://github.com/depthso
	@license MIT
	@description Sigma Remote Spy for Roblox
    
	This file is COMPILED, check /src folder for the source
	Build scripts are available in /build
]]

local a,b={UseWorkspace=false,NoActors=false,FolderName='Sigma Spy',RepoUrl=
[[https://raw.githubusercontent.com/depthso/Sigma-Spy/refs/heads/main]],
ParserUrl=
[[https://raw.githubusercontent.com/depthso/Roblox-parser/refs/heads/main]]},{
...}local c=b[1]if typeof(c)=='table'then for d,e in c do a[d]=e end end local d
=setmetatable({},{__index=function(d,e)local f=game:GetService(e)return
cloneref(f)end})local e=(function()local e,f={UseWorkspace=false,Folder=
'Sigma spy',RepoUrl=nil,FolderStructure={['Sigma Spy']={'assets'}}}function e:
Init(g)local h,i=self.FolderStructure,g.Services f=i.HttpService self:
CheckFolders(h)end function e:PushConfig(g)for h,i in next,g do self[h]=i end
end function e:UrlFetch(g)local h={Url=g:gsub(' ','%%20'),Method='GET'}local i,j
=pcall(request,h)if not i then warn'[!] HTTP request error! Check console (F9)'
warn('> Url:',g)error(j)return''end local k,l=j.Body,j.StatusCode if l==404 then
warn'[!] The file requested has moved or been deleted.'warn(' >',g)return''end
return k,j end function e:MakePath(g)local h=self.Folder return`{h}/{g}`end
function e:LoadCustomasset(g)if not getcustomasset then return end if not g then
return end local h=readfile(g)if#h<=0 then return end local i,j=pcall(
getcustomasset,g)if not i then return end if not j or#j<=0 then return end
return j end function e:GetFile(g,h)local i,j,k,l=self.RepoUrl,self.UseWorkspace
,self:MakePath(g),''if j then l=readfile(k)else l=self:UrlFetch(`{i}/{g}`)end if
h then self:FileCheck(k,function()return l end)return self:LoadCustomasset(k)end
return l end function e:GetTemplate(g)return self:GetFile(`templates/{g}.lua`)
end function e:FileCheck(g,h)if isfile(g)then return end local i=h()writefile(g,
i)end function e:FolderCheck(g)if isfolder(g)then return end makefolder(g)end
function e:CheckPath(g,h)return g and`{g}/{h}`or h end function e:CheckFolders(g
,h)for i,j in next,g do if typeof(j)=='table'then local k=self:CheckPath(h,i)
self:FolderCheck(k)self:CheckFolders(j,k)continue end local k=self:CheckPath(h,j
)self:FolderCheck(k)end end function e:TemplateCheck(g,h)self:FileCheck(g,
function()return self:GetTemplate(h)end)end function e:GetAsset(g,h)return self:
GetFile(`assets/{g}`,h)end function e:GetModule(g,h)local i=`{g}.lua`if h then
self:TemplateCheck(i,h)return readfile(i)end return self:GetFile(i)end function
e:LoadLibraries(g,...)local h={}for i,j in next,g do local k=typeof(j)=='table'
and j[1]=='base64'j=k and j[2]or j if typeof(j)~='string'and not k then h[i]=j
continue end if k then j=crypt.base64decode(j)g[i]=j end local l=loadstring(j,i)
assert(l,`Failed to load {i}`)h[i]=l(...)end return h end function e:LoadModules
(g,h)for i,j in next,g do local k=j.Init if not k then continue end j:Init(h)end
end function e:CreateFont(g,h)if not h then return end local i=`assets/{g}.json`
local j,k=self:MakePath(i),{name=g,faces={{name='Regular',weight=400,style=
'Normal',assetId=h}}}local l=f:JSONEncode(k)writefile(j,l)return j end function
e:CompileModule(g)local h='local Libraries = {'for i,j in g do if typeof(j)~=
'string'then continue end h..=`\t{i} = (function()\n{j}\nend)(),\n`end h..='}'
return h end function e:MakeActorScript(g,h)local i=e:CompileModule(g)i..=
'\r\n\tlocal ExtraData = {\r\n\t\tIsActor = true\r\n\t}\r\n\t'i..=`Libraries.Hook:BeginService(Libraries, ExtraData, {
h})`return i end return e end)()e:PushConfig(a)e:Init{Services=d}local f=e.
FolderName local g,h={Config=e:GetModule(`{f}/Config`,'Config'),ReturnSpoofs=e:
GetModule(`{f}/Return spoofs`,'Return Spoofs'),Configuration=a,Files=e,Process={
'base64',
[[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]]
},Hook={'base64',
[[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]]
},Flags={'base64',
[[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]]
},Ui={'base64',
[[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]]
},Generation={'base64',
[[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]]
},Communication={'base64',
[[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]]
}},d.Players local i=e:LoadLibraries(g)local j,k,l,m,n,o,p=i.Process,i.Hook,i.Ui
,i.Generation,i.Communication,i.Config,e:GetAsset('ProggyClean.ttf',true)local q
=e:CreateFont('ProggyClean',p)l:SetFontFile(q)j:CheckConfig(o)e:LoadModules(i,{
Modules=i,Services=d})local r,s=l:CreateMainWindow(),j:CheckIsSupported()if not
s then r:Close()return end local t,u=n:CreateChannel()n:AddCommCallback(
'QueueLog',function(...)l:QueueLog(...)end)local v=h.LocalPlayer m:
SetSwapsCallback(function(w)w:AddSwap(v,{String='LocalPlayer'})w:AddSwap(v.
Character,{String='Character',NextParent=v})end)l:CreateWindowContent(r)l:
SetCommChannel(u)l:BeginLogService()local w=e:MakeActorScript(g,t)k:LoadHooks(w,
t)