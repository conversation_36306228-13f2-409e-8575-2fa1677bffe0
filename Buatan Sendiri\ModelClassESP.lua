

CMDs[#CMDs + 1] = {NAME = 'modelesp [model name]', DESC = 'Highlights a model'}
CMDs[#CMDs + 1] = {NAME = 'unmodelesp / nomodelesp [model name]', DESC = 'removes modelesp'}
CMDs[#CMDs + 1] = {NAME = 'classesp [class name]', DESC = 'Highlights objects by class'}
CMDs[#CMDs + 1] = {NAME = 'unclassesp / noclassesp [class name]', DESC = 'removes classesp'}

local espModels = {}
local espClasses = {}

local modelEspTrigger = nil
local classEspTrigger = nil

function modelAdded(model)
	if #espModels > 0 then
		if FindInTable(espModels, model.Name:lower()) then
		
			if model:IsA("Model") then
				local primaryPart = model.PrimaryPart or model:FindFirstChildOfClass("BasePart")
				if primaryPart then
					local a = Instance.new("BoxHandleAdornment")
					a.Name = model.Name:lower().."_MESP"
					a.Parent = primaryPart
					a.Adornee = primaryPart
					a.AlwaysOnTop = true
					a.ZIndex = 0
					a.Size = primaryPart.Size
					a.Transparency = espTransparency
					a.Color = BrickColor.new("Bright blue")
				end
			end
		end
	else
		if modelEspTrigger then
			modelEspTrigger:Disconnect()
			modelEspTrigger = nil
		end
	end
end

function classAdded(obj)
	if #espClasses > 0 then
		if FindInTable(espClasses, obj.ClassName:lower()) then
		
			if obj:IsA("BasePart") then
				local a = Instance.new("BoxHandleAdornment")
				a.Name = obj.ClassName:lower().."_CESP"
				a.Parent = obj
				a.Adornee = obj
				a.AlwaysOnTop = true
				a.ZIndex = 0
				a.Size = obj.Size
				a.Transparency = espTransparency
				a.Color = BrickColor.new("Bright yellow")
			elseif obj:IsA("Model") then
				local primaryPart = obj.PrimaryPart or obj:FindFirstChildOfClass("BasePart")
				if primaryPart then
					local a = Instance.new("BoxHandleAdornment")
					a.Name = obj.ClassName:lower().."_CESP"
					a.Parent = primaryPart
					a.Adornee = primaryPart
					a.AlwaysOnTop = true
					a.ZIndex = 0
					a.Size = primaryPart.Size
					a.Transparency = espTransparency
					a.Color = BrickColor.new("Bright yellow")
				end
			end
		end
	else
		if classEspTrigger then
			classEspTrigger:Disconnect()
			classEspTrigger = nil
		end
	end
end

addcmd('modelesp',{},function(args, speaker)
	local modelEspName = getstring(1):lower()
	if not FindInTable(espModels, modelEspName) then
		table.insert(espModels, modelEspName)
	
		for i,v in pairs(workspace:GetDescendants()) do
			if v:IsA("Model") and v.Name:lower() == modelEspName then
				local primaryPart = v.PrimaryPart or v:FindFirstChildOfClass("BasePart")
				if primaryPart then
					local a = Instance.new("BoxHandleAdornment")
					a.Name = modelEspName.."_MESP"
					a.Parent = primaryPart
					a.Adornee = primaryPart
					a.AlwaysOnTop = true
					a.ZIndex = 0
					a.Size = primaryPart.Size
					a.Transparency = espTransparency
					a.Color = BrickColor.new("Bright blue")
				end
			end
		end
	end
	if modelEspTrigger == nil then
		modelEspTrigger = workspace.DescendantAdded:Connect(modelAdded)
	end
end)

addcmd('unmodelesp',{'nomodelesp'},function(args, speaker)
	if args[1] then
		local modelEspName = getstring(1):lower()
		if FindInTable(espModels, modelEspName) then
			table.remove(espModels, GetInTable(espModels, modelEspName))
		end
		for i,v in pairs(workspace:GetDescendants()) do
			if v:IsA("BoxHandleAdornment") and v.Name == modelEspName..'_MESP' then
				v:Destroy()
			end
		end
	else
		if modelEspTrigger then
			modelEspTrigger:Disconnect()
			modelEspTrigger = nil
		end
		espModels = {}
		for i,v in pairs(workspace:GetDescendants()) do
			if v:IsA("BoxHandleAdornment") and v.Name:sub(-5) == '_MESP' then
				v:Destroy()
			end
		end
	end
end)

addcmd('classesp',{},function(args, speaker)
	local classEspName = getstring(1):lower()
	if not FindInTable(espClasses, classEspName) then
		table.insert(espClasses, classEspName)
	
		for i,v in pairs(workspace:GetDescendants()) do
			if v.ClassName:lower() == classEspName then
				if v:IsA("BasePart") then
					local a = Instance.new("BoxHandleAdornment")
					a.Name = classEspName.."_CESP"
					a.Parent = v
					a.Adornee = v
					a.AlwaysOnTop = true
					a.ZIndex = 0
					a.Size = v.Size
					a.Transparency = espTransparency
					a.Color = BrickColor.new("Bright yellow")
				elseif v:IsA("Model") then
					local primaryPart = v.PrimaryPart or v:FindFirstChildOfClass("BasePart")
					if primaryPart then
						local a = Instance.new("BoxHandleAdornment")
						a.Name = classEspName.."_CESP"
						a.Parent = primaryPart
						a.Adornee = primaryPart
						a.AlwaysOnTop = true
						a.ZIndex = 0
						a.Size = primaryPart.Size
						a.Transparency = espTransparency
						a.Color = BrickColor.new("Bright yellow")
					end
				end
			end
		end
	end
	if classEspTrigger == nil then
		classEspTrigger = workspace.DescendantAdded:Connect(classAdded)
	end
end)

addcmd('unclassesp',{'noclassesp'},function(args, speaker)
	if args[1] then
		local classEspName = getstring(1):lower()
		if FindInTable(espClasses, classEspName) then
			table.remove(espClasses, GetInTable(espClasses, classEspName))
		end
		for i,v in pairs(workspace:GetDescendants()) do
			if v:IsA("BoxHandleAdornment") and v.Name == classEspName..'_CESP' then
				v:Destroy()
			end
		end
	else
		if classEspTrigger then
			classEspTrigger:Disconnect()
			classEspTrigger = nil
		end
		espClasses = {}
		for i,v in pairs(workspace:GetDescendants()) do
			if v:IsA("BoxHandleAdornment") and v.Name:sub(-5) == '_CESP' then
				v:Destroy()
			end
		end
	end
end)
