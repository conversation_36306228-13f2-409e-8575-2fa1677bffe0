-- Firetouchinterest Script dengan Rayfield GUI
local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Daftar semua part names
local partNames = {
    "bicolor", "tricolor", "triangle", "rubro", "japan", "fusion", "square",
    "psg_world", "psg_world2", "psg_world3", "alhilal_world", "alhilal_world2",
    "bayer_world", "bayer_world2", "chelsea_world", "chelsea_world2",
    "borussia_world", "borussia_world2", "flamengo_world", "flamengo_world2",
    "flamengo_cruz", "flamengo_simbol", "palmeiras_world", "palmeiras_world3",
    "palmeiras_world2", "botafogo_world", "botafogo_world3", "botafogo_world2",
    "fluminense_world", "fluminense_world2", "redyellowray", "flamengo_listrado",
    "redyellow", "brazil4", "thailand", "jamaica", "roblox", "love",
    "tricolor_brazil", "tricolor_2", "cruz", "colored", "colored2", "arrow",
    "epslon", "abstract", "epslon2", "tezinha", "tronador", "v2_negro",
    "v2_vermelho", "valpo", "valpo_2", "aviao_amarelo", "aviao_azul",
    "aviao_branco", "aviao_brasil", "aviao_verde", "aviao_vermelho", "cavera",
    "cruzados", "cruzados_2", "cruzados_3", "escopiao", "escorpiao_amarelo",
    "escorpiao_negro", "falcao_azul", "falcao_vermelho", "fantasia",
    "garra_branca", "garra_verde", "golfinho", "golfinho_amarelo",
    "golfinho_colorido", "golfinho_colorido_2", "golfinho_negro", "raio_negro",
    "raio_vermelho", "ronald_rojoam", "vespa", "vespa_negra", "flamengo",
    "botafogo", "palmeiras", "fluminense", "atletico_go", "atletico_mg",
    "atletico_pr", "bahia", "ceara", "corinthians", "cruzeiro", "fortaleza",
    "gremio", "internacional", "santos", "sao_paulo", "vasco", "barcelona",
    "real_madrid", "boca_juniors", "juventus", "penarol", "river_plate",
    "brazil", "argentina", "france", "netherlands", "england", "italy",
    "portugal", "united_states", "uruguai", "dragon_ball", "uchiha", "escudo",
    "batman", "batman_2", "batman_3", "batman_4", "maravilha", "maravilha_3",
    "superman", "superman_2", "superman_3", "superman_4", "homem_ferro",
    "homem_ferro_2", "homem_ferro_3", "homem_ferro_4", "homem_aranha",
    "homem_aranha_2", "homem_aranha_3", "homem_aranha_4", "thunder",
    "thunder_cats", "christmas"
}

-- Variables
local autoFireEnabled = false
local selectedParts = {}
local player = game.Players.LocalPlayer

-- Function untuk mencari part berdasarkan nama
local function findPart(partName)
    for _, obj in pairs(workspace:GetDescendants()) do
        if obj:IsA("BasePart") and obj.Name == partName then
            return obj
        end
    end
    return nil
end

-- Function untuk fire touch interest (seperti Infinite Yield)
local function fireTouchInterest(part)
    if part and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        firetouchinterest(part, player.Character.HumanoidRootPart, 0)
        firetouchinterest(part, player.Character.HumanoidRootPart, 1)
    end
end

-- Function untuk fire semua part yang dipilih (dengan delay 0.5 detik)
local function fireSelectedParts()
    for partName, _ in pairs(selectedParts) do
        local part = findPart(partName)
        if part then
            fireTouchInterest(part)
            print("Fired touch interest for:", partName)
            wait(0.5) -- Delay 0.5 detik untuk setiap fire
        else
            print("Part not found in workspace:", partName)
        end
    end
end

-- Function untuk auto fire loop (optimized seperti Infinite Yield)
local function autoFireLoop()
    while autoFireEnabled do
        -- Fire semua part sekaligus tanpa delay individual
        for partName, _ in pairs(selectedParts) do
            if not autoFireEnabled then break end

            local part = findPart(partName)
            if part then
                fireTouchInterest(part)
                print("Auto fired:", partName)
            else
                print("Part not found in workspace:", partName)
            end
        end

        -- Delay 0.5 detik setelah semua part di-fire
        wait(0.5)

        -- Jika tidak ada part yang dipilih, tunggu sebentar sebelum check lagi
        if next(selectedParts) == nil then
            wait(1)
        end
    end
end

-- Create Rayfield Window
local Window = Rayfield:CreateWindow({
    Name = "Firetouchinterest Script",
    LoadingTitle = "Loading Script...",
    LoadingSubtitle = "by YourName",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "FireTouchScript",
        FileName = "config"
    }
})

-- Create Tab
local MainTab = Window:CreateTab("Main", 4483362458)

-- Auto Fire Toggle
local AutoFireToggle = MainTab:CreateToggle({
    Name = "Auto Fire",
    CurrentValue = false,
    Flag = "AutoFireToggle",
    Callback = function(Value)
        autoFireEnabled = Value
        if Value then
            spawn(autoFireLoop)
            print("Auto fire enabled")
        else
            print("Auto fire disabled")
        end
    end,
})

-- Fire Once Button
local FireOnceButton = MainTab:CreateButton({
    Name = "Fire Selected Parts Once",
    Callback = function()
        fireSelectedParts()
        print("Fired all selected parts once")
    end,
})

-- Create Parts Tab
local PartsTab = Window:CreateTab("Parts Selection", 4483362458)

-- Create toggles untuk setiap part (dalam chunks untuk menghindari lag)
local function createPartToggles()
    for i, partName in ipairs(partNames) do
        PartsTab:CreateToggle({
            Name = partName,
            CurrentValue = false,
            Flag = partName .. "Toggle",
            Callback = function(Value)
                if Value then
                    selectedParts[partName] = true
                    print("Selected:", partName)
                else
                    selectedParts[partName] = nil
                    print("Deselected:", partName)
                end
            end,
        })
        
        -- Yield setiap 10 toggles untuk mencegah lag
        if i % 10 == 0 then
            wait()
        end
    end
end

-- Create part toggles
spawn(createPartToggles)

-- Info Tab
local InfoTab = Window:CreateTab("Info", 4483362458)

InfoTab:CreateParagraph({
    Title = "How to Use",
    Content = "1. Select parts you want to fire from 'Parts Selection' tab\n2. Use 'Select All' to select all parts at once\n3. Enable 'Auto Fire' for continuous firing\n4. Use 'Fire Selected Parts Once' for single fire\n5. Use 'Deselect All' to clear selection"
})

InfoTab:CreateParagraph({
    Title = "Script Info",
    Content = "This script uses firetouchinterest to simulate touching parts in the workspace. Make sure you have the required exploits/executors that support this function."
})

print("Firetouchinterest Script loaded successfully!")
print("Total parts available:", #partNames)

