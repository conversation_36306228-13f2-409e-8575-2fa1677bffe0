-- RainbowShells ESP Script
-- Script untuk menampilkan ESP pada semua RainbowShell dengan toggle individual

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer:WaitForChild("PlayerGui")

-- Konfigurasi ESP
local ESPConfig = {
    Enabled = true,
    TextColor = Color3.fromRGB(255, 255, 0), -- Kuning
    TextSize = 14,
    Font = Enum.Font.SourceSansBold,
    ShowDistance = true,
    MaxDistance = 1000 -- Jarak maksimal untuk menampilkan ESP
}

-- Storage untuk ESP objects dan status
local ESPObjects = {}
local ShellToggles = {}
local RainbowShells = {}

-- GUI Setup
local ScreenGui = Instance.new("ScreenGui")
ScreenGui.Name = "RainbowShellsESP"
ScreenGui.Parent = PlayerGui
ScreenGui.ResetOnSpawn = false

-- Main Frame
local MainFrame = Instance.new("Frame")
MainFrame.Name = "MainFrame"
MainFrame.Parent = ScreenGui
MainFrame.BackgroundColor3 = Color3.fromRGB(36, 36, 37)
MainFrame.BorderSizePixel = 0
MainFrame.Position = UDim2.new(0, 10, 0, 10)
MainFrame.Size = UDim2.new(0, 250, 0, 400)
MainFrame.Active = true
MainFrame.Draggable = true

-- Title
local Title = Instance.new("TextLabel")
Title.Name = "Title"
Title.Parent = MainFrame
Title.BackgroundColor3 = Color3.fromRGB(46, 46, 47)
Title.BorderSizePixel = 0
Title.Size = UDim2.new(1, 0, 0, 30)
Title.Font = Enum.Font.SourceSansBold
Title.Text = "RainbowShells ESP"
Title.TextColor3 = Color3.new(1, 1, 1)
Title.TextSize = 16

-- Close Button
local CloseButton = Instance.new("TextButton")
CloseButton.Name = "CloseButton"
CloseButton.Parent = Title
CloseButton.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
CloseButton.BorderSizePixel = 0
CloseButton.Position = UDim2.new(1, -25, 0, 5)
CloseButton.Size = UDim2.new(0, 20, 0, 20)
CloseButton.Font = Enum.Font.SourceSansBold
CloseButton.Text = "X"
CloseButton.TextColor3 = Color3.new(1, 1, 1)
CloseButton.TextSize = 14

-- Master Toggle
local MasterToggle = Instance.new("TextButton")
MasterToggle.Name = "MasterToggle"
MasterToggle.Parent = MainFrame
MasterToggle.BackgroundColor3 = Color3.fromRGB(0, 170, 0)
MasterToggle.BorderSizePixel = 0
MasterToggle.Position = UDim2.new(0, 10, 0, 40)
MasterToggle.Size = UDim2.new(1, -20, 0, 25)
MasterToggle.Font = Enum.Font.SourceSans
MasterToggle.Text = "ESP: ON"
MasterToggle.TextColor3 = Color3.new(1, 1, 1)
MasterToggle.TextSize = 14

-- Scroll Frame untuk daftar shells
local ScrollFrame = Instance.new("ScrollingFrame")
ScrollFrame.Name = "ScrollFrame"
ScrollFrame.Parent = MainFrame
ScrollFrame.BackgroundColor3 = Color3.fromRGB(46, 46, 47)
ScrollFrame.BorderSizePixel = 0
ScrollFrame.Position = UDim2.new(0, 5, 0, 75)
ScrollFrame.Size = UDim2.new(1, -10, 1, -80)
ScrollFrame.ScrollBarThickness = 8
ScrollFrame.CanvasSize = UDim2.new(0, 0, 0, 0)

-- Layout untuk scroll frame
local UIListLayout = Instance.new("UIListLayout")
UIListLayout.Parent = ScrollFrame
UIListLayout.SortOrder = Enum.SortOrder.Name
UIListLayout.Padding = UDim.new(0, 2)

-- Function untuk membuat ESP TextLabel
local function createESPLabel(shell, index)
    local BillboardGui = Instance.new("BillboardGui")
    BillboardGui.Name = "RainbowShellESP_" .. index
    BillboardGui.Parent = shell
    BillboardGui.Size = UDim2.new(0, 200, 0, 50)
    BillboardGui.StudsOffset = Vector3.new(0, 2, 0)
    BillboardGui.AlwaysOnTop = true
    
    local TextLabel = Instance.new("TextLabel")
    TextLabel.Parent = BillboardGui
    TextLabel.BackgroundTransparency = 1
    TextLabel.Size = UDim2.new(1, 0, 1, 0)
    TextLabel.Font = ESPConfig.Font
    TextLabel.TextColor3 = ESPConfig.TextColor
    TextLabel.TextSize = ESPConfig.TextSize
    TextLabel.TextStrokeTransparency = 0
    TextLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
    TextLabel.Text = "RainbowShell" .. index
    
    return BillboardGui, TextLabel
end

-- Function untuk update jarak pada ESP
local function updateESPDistance(textLabel, shell, index)
    if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        local distance = (LocalPlayer.Character.HumanoidRootPart.Position - shell.Position).Magnitude
        if distance <= ESPConfig.MaxDistance then
            if ESPConfig.ShowDistance then
                textLabel.Text = "RainbowShell" .. index .. "\n[" .. math.floor(distance) .. "m]"
            else
                textLabel.Text = "RainbowShell" .. index
            end
            textLabel.Parent.Parent.Enabled = true
        else
            textLabel.Parent.Parent.Enabled = false
        end
    end
end

-- Function untuk membuat toggle button untuk setiap shell
local function createShellToggle(shell, index)
    local ToggleButton = Instance.new("TextButton")
    ToggleButton.Name = "Shell" .. index .. "Toggle"
    ToggleButton.Parent = ScrollFrame
    ToggleButton.BackgroundColor3 = Color3.fromRGB(0, 170, 0)
    ToggleButton.BorderSizePixel = 0
    ToggleButton.Size = UDim2.new(1, -10, 0, 25)
    ToggleButton.Font = Enum.Font.SourceSans
    ToggleButton.Text = "RainbowShell" .. index .. ": ON"
    ToggleButton.TextColor3 = Color3.new(1, 1, 1)
    ToggleButton.TextSize = 12
    
    -- Status toggle (default: true)
    ShellToggles[index] = true
    
    -- Event handler untuk toggle
    ToggleButton.MouseButton1Click:Connect(function()
        ShellToggles[index] = not ShellToggles[index]
        
        if ShellToggles[index] then
            ToggleButton.BackgroundColor3 = Color3.fromRGB(0, 170, 0)
            ToggleButton.Text = "RainbowShell" .. index .. ": ON"
            
            -- Aktifkan ESP
            if ESPObjects[index] then
                ESPObjects[index].Enabled = ESPConfig.Enabled
            end
        else
            ToggleButton.BackgroundColor3 = Color3.fromRGB(170, 0, 0)
            ToggleButton.Text = "RainbowShell" .. index .. ": OFF"
            
            -- Nonaktifkan ESP
            if ESPObjects[index] then
                ESPObjects[index].Enabled = false
            end
        end
    end)
    
    return ToggleButton
end

-- Function untuk scan dan setup ESP pada semua RainbowShells
local function setupRainbowShellsESP()
    -- Clear existing ESP
    for _, espObj in pairs(ESPObjects) do
        if espObj then
            espObj:Destroy()
        end
    end
    ESPObjects = {}
    
    -- Clear existing toggles
    for _, child in pairs(ScrollFrame:GetChildren()) do
        if child:IsA("TextButton") then
            child:Destroy()
        end
    end
    
    -- Cari semua RainbowShells
    local collectibles = workspace:FindFirstChild("Collectibles")
    if collectibles then
        local rainbowShellsFolder = collectibles:FindFirstChild("RainbowShells")
        if rainbowShellsFolder then
            local shells = rainbowShellsFolder:GetChildren()
            RainbowShells = {}
            
            for i, shell in ipairs(shells) do
                if shell.Name == "RainbowShell" then
                    table.insert(RainbowShells, shell)
                    
                    -- Buat ESP
                    local billboardGui, textLabel = createESPLabel(shell, i)
                    ESPObjects[i] = billboardGui
                    
                    -- Buat toggle button
                    createShellToggle(shell, i)
                end
            end
            
            -- Update canvas size
            ScrollFrame.CanvasSize = UDim2.new(0, 0, 0, #RainbowShells * 27)
        end
    end
end

-- Event handlers
CloseButton.MouseButton1Click:Connect(function()
    ScreenGui:Destroy()
end)

MasterToggle.MouseButton1Click:Connect(function()
    ESPConfig.Enabled = not ESPConfig.Enabled
    
    if ESPConfig.Enabled then
        MasterToggle.BackgroundColor3 = Color3.fromRGB(0, 170, 0)
        MasterToggle.Text = "ESP: ON"
    else
        MasterToggle.BackgroundColor3 = Color3.fromRGB(170, 0, 0)
        MasterToggle.Text = "ESP: OFF"
    end
    
    -- Update semua ESP
    for i, espObj in pairs(ESPObjects) do
        if espObj and ShellToggles[i] then
            espObj.Enabled = ESPConfig.Enabled
        end
    end
end)

-- Update loop untuk jarak
local connection
connection = RunService.Heartbeat:Connect(function()
    if ESPConfig.Enabled then
        for i, shell in ipairs(RainbowShells) do
            if ESPObjects[i] and ShellToggles[i] and shell.Parent then
                local textLabel = ESPObjects[i]:FindFirstChild("TextLabel")
                if textLabel then
                    updateESPDistance(textLabel, shell, i)
                end
            end
        end
    end
end)

-- Cleanup ketika GUI dihancurkan
ScreenGui.AncestryChanged:Connect(function()
    if not ScreenGui.Parent then
        connection:Disconnect()
    end
end)

-- Setup awal
setupRainbowShellsESP()

-- Refresh button (opsional, untuk re-scan shells)
local RefreshButton = Instance.new("TextButton")
RefreshButton.Name = "RefreshButton"
RefreshButton.Parent = MainFrame
RefreshButton.BackgroundColor3 = Color3.fromRGB(0, 100, 200)
RefreshButton.BorderSizePixel = 0
RefreshButton.Position = UDim2.new(0, 130, 0, 40)
RefreshButton.Size = UDim2.new(0, 60, 0, 25)
RefreshButton.Font = Enum.Font.SourceSans
RefreshButton.Text = "Refresh"
RefreshButton.TextColor3 = Color3.new(1, 1, 1)
RefreshButton.TextSize = 12

RefreshButton.MouseButton1Click:Connect(function()
    setupRainbowShellsESP()
end)

print("RainbowShells ESP loaded successfully!")
