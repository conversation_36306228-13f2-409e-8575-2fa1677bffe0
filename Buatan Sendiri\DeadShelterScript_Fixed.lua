local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer
local Camera = Workspace.CurrentCamera
local PlayerGui = LocalPlayer:WaitForChild("PlayerGui")
local THEME_RED = Color3.fromRGB(229, 9, 20)
local THEME_BLACK = Color3.fromRGB(20, 20, 20)
local THEME_DARK_GRAY = Color3.fromRGB(35, 35, 35)
local THEME_LIGHT_GRAY = Color3.fromRGB(70, 70, 70)
local THEME_WHITE = Color3.fromRGB(255, 255, 255)
local THEME_GRAY_BLUE = Color3.fromRGB(30, 35, 45)
local THEME_GREEN = Color3.fromRGB(0, 255, 0)
local THEME_YELLOW = Color3.fromRGB(255, 255, 0)
local THEME_ORANGE = Color3.fromRGB(255, 165, 0)
local ESP_CONFIG = {
    Items = {
        TextColor = THEME_GREEN,
        TextSize = 9,
        Font = Enum.Font.SourceSansBold,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0),
        Enabled = false,
        MaxDistance = 500
    },
    Enemies = {
        TextColor = THEME_RED,
        TextSize = 9,
        Font = Enum.Font.SourceSansBold,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0),
        Enabled = false,
        MaxDistance = 150
    },
    MapAreas = {
        TextColor = THEME_YELLOW,
        TextSize = 9,
        Font = Enum.Font.SourceSansBold,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0),
        Enabled = false,
        MaxDistance = 500
    }
}
local Settings = {
    ItemESP = false,
    EnemyESP = false,
    MapAreaESP = false,
    ShowDistance = true,
    Transparency = 0.2,
    DragSpeed = 0.05,
    MinimizedState = false,
    UISize = UDim2.new(0, 500, 0, 550),
    GUIVisible = true
}
local espObjects = {
    items = {},
    enemies = {},
    mapAreas = {}
}
local individualESPSettings = {
    items = {},
    enemies = {},
    mapAreas = {}
}
local itemSearchQuery = ""
local TELEPORT_LOCATIONS = {
    Base = {
        name = "Base",
        target = "Baseplate"
    },
    Pawnshop = {
        name = "Pawnshop",
        target = "Noob's Pawnshop"
    }
}

local GUI = nil
local function CreateElement(className, properties, children)
    local element = Instance.new(className)

    for property, value in pairs(properties) do
        element[property] = value
    end

    if children then
        for _, child in pairs(children) do
            child.Parent = element
        end
    end

    return element
end

local function getDistance(object)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        return math.huge
    end

    local playerPosition = LocalPlayer.Character.HumanoidRootPart.Position
    local objectPosition

    if object:IsA("Model") and object.PrimaryPart then
        objectPosition = object.PrimaryPart.Position
    elseif object:IsA("Part") then
        objectPosition = object.Position
    elseif object:IsA("Model") then
        local part = object:FindFirstChildOfClass("Part")
        if part then
            objectPosition = part.Position
        else
            return math.huge
        end
    else
        return math.huge
    end

    return (playerPosition - objectPosition).Magnitude
end

local function teleportToObject(object)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        warn("Player character or HumanoidRootPart not found!")
        return false
    end

    local targetPosition
    if object:IsA("Model") and object.PrimaryPart then
        targetPosition = object.PrimaryPart.Position
    elseif object:IsA("Part") then
        targetPosition = object.Position
    elseif object:IsA("Model") then
        local part = object:FindFirstChildOfClass("Part")
        if part then
            targetPosition = part.Position
        else
            warn("No valid part found in " .. object.Name)
            return false
        end
    else
        warn("Invalid object type for " .. object.Name)
        return false
    end

    local offsetPosition = targetPosition + Vector3.new(0, 5, 0)
    LocalPlayer.Character.HumanoidRootPart.CFrame = CFrame.new(offsetPosition)
    return true
end

local function createItemESP(item)
    local billboardGui =
        CreateElement(
        "BillboardGui",
        {
            Name = "ItemESP",
            Adornee = item:IsA("Model") and (item.PrimaryPart or item:FindFirstChildOfClass("Part")) or item,
            Size = UDim2.new(0, 200, 0, 50),
            StudsOffset = Vector3.new(0, 3, 0),
            AlwaysOnTop = true,
            Parent = item
        }
    )

    local textLabel =
        CreateElement(
        "TextLabel",
        {
            Name = "ESPText",
            Size = UDim2.new(1, 0, 1, 0),
            BackgroundTransparency = 1,
            Text = item.Name,
            TextColor3 = ESP_CONFIG.Items.TextColor,
            TextSize = ESP_CONFIG.Items.TextSize,
            Font = ESP_CONFIG.Items.Font,
            TextStrokeTransparency = ESP_CONFIG.Items.Outline and 0 or 1,
            TextStrokeColor3 = ESP_CONFIG.Items.OutlineColor,
            TextScaled = true,
            Parent = billboardGui
        }
    )

    return billboardGui
end

local function createEnemyESP(enemy)
    local billboardGui =
        CreateElement(
        "BillboardGui",
        {
            Name = "EnemyESP",
            Adornee = enemy:IsA("Model") and (enemy.PrimaryPart or enemy:FindFirstChildOfClass("Part")) or enemy,
            Size = UDim2.new(0, 200, 0, 60),
            StudsOffset = Vector3.new(0, 4, 0),
            AlwaysOnTop = true,
            Parent = enemy
        }
    )

    local textLabel =
        CreateElement(
        "TextLabel",
        {
            Name = "ESPText",
            Size = UDim2.new(1, 0, 1, 0),
            BackgroundTransparency = 1,
            Text = "ENEMY\n" .. enemy.Name,
            TextColor3 = ESP_CONFIG.Enemies.TextColor,
            TextSize = ESP_CONFIG.Enemies.TextSize,
            Font = ESP_CONFIG.Enemies.Font,
            TextStrokeTransparency = ESP_CONFIG.Enemies.Outline and 0 or 1,
            TextStrokeColor3 = ESP_CONFIG.Enemies.OutlineColor,
            TextScaled = true,
            Parent = billboardGui
        }
    )

    return billboardGui
end

local function createMapAreaESP(mapArea)
    local billboardGui =
        CreateElement(
        "BillboardGui",
        {
            Name = "MapAreaESP",
            Adornee = mapArea:IsA("Model") and (mapArea.PrimaryPart or mapArea:FindFirstChildOfClass("Part")) or mapArea,
            Size = UDim2.new(0, 200, 0, 50),
            StudsOffset = Vector3.new(0, 5, 0),
            AlwaysOnTop = true,
            Parent = mapArea
        }
    )

    local textLabel =
        CreateElement(
        "TextLabel",
        {
            Name = "ESPText",
            Size = UDim2.new(1, 0, 1, 0),
            BackgroundTransparency = 1,
            Text = "AREA\n" .. mapArea.Name,
            TextColor3 = ESP_CONFIG.MapAreas.TextColor,
            TextSize = ESP_CONFIG.MapAreas.TextSize,
            Font = ESP_CONFIG.MapAreas.Font,
            TextStrokeTransparency = ESP_CONFIG.MapAreas.Outline and 0 or 1,
            TextStrokeColor3 = ESP_CONFIG.MapAreas.OutlineColor,
            TextScaled = true,
            Parent = billboardGui
        }
    )

    return billboardGui
end

local function updateESPVisibility()
    for item, espGui in pairs(espObjects.items) do
        if item.Parent and espGui.Parent then
            local distance = getDistance(item)
            local individualEnabled = individualESPSettings.items[item.Name] ~= false
            if distance <= ESP_CONFIG.Items.MaxDistance and Settings.ItemESP and individualEnabled then
                espGui.Enabled = true
                if Settings.ShowDistance then
                    local textLabel = espGui:FindFirstChild("ESPText")
                    if textLabel then
                        -- textLabel.Text = string.format("\n%s\nDistance: %.1fm", item.Name, distance)
                        textLabel.Text = string.format("\n%s\n", item.Name)
                    end
                end
            else
                espGui.Enabled = false
            end
        else
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.items[item] = nil
        end
    end

    for enemy, espGui in pairs(espObjects.enemies) do
        if enemy.Parent and espGui.Parent then
            local distance = getDistance(enemy)
            local individualEnabled = individualESPSettings.enemies[enemy.Name] ~= false
            if distance <= ESP_CONFIG.Enemies.MaxDistance and Settings.EnemyESP and individualEnabled then
                espGui.Enabled = true
                if Settings.ShowDistance then
                    local textLabel = espGui:FindFirstChild("ESPText")
                    if textLabel then
                        textLabel.Text = string.format("\n%s\nDistance: %.1fm", enemy.Name, distance)
                    end
                end
            else
                espGui.Enabled = false
            end
        else
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.enemies[enemy] = nil
        end
    end

    for mapArea, espGui in pairs(espObjects.mapAreas) do
        if mapArea.Parent and espGui.Parent then
            local distance = getDistance(mapArea)
            local individualEnabled = individualESPSettings.mapAreas[mapArea.Name] ~= false
            if distance <= ESP_CONFIG.MapAreas.MaxDistance and Settings.MapAreaESP and individualEnabled then
                espGui.Enabled = true
                if Settings.ShowDistance then
                    local textLabel = espGui:FindFirstChild("ESPText")
                    if textLabel then
                        textLabel.Text = string.format("AREA\n%s\nDistance: %.1fm", mapArea.Name, distance)
                    end
                end
            else
                espGui.Enabled = false
            end
        else
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.mapAreas[mapArea] = nil
        end
    end
end

local function scanItems()
    local draggableFolder = Workspace:FindFirstChild("DraggableFolder")
    if not draggableFolder then
        warn("DraggableFolder not found in Workspace!")
        return
    end

    local foundItems = 0
    for _, item in pairs(draggableFolder:GetChildren()) do
        if not espObjects.items[item] then
            local espGui = createItemESP(item)
            espObjects.items[item] = espGui
            foundItems = foundItems + 1

            -- Initialize individual ESP setting if not exists
            if individualESPSettings.items[item.Name] == nil then
                individualESPSettings.items[item.Name] = true
            end
        end
    end
end

local function scanEnemies()
    local enemiesFolder = Workspace:FindFirstChild("Enemies")
    if not enemiesFolder then
        warn("Enemies folder not found in Workspace!")
        return
    end

    local foundEnemies = 0
    for _, enemy in pairs(enemiesFolder:GetChildren()) do
        if not espObjects.enemies[enemy] then
            local espGui = createEnemyESP(enemy)
            espObjects.enemies[enemy] = espGui
            foundEnemies = foundEnemies + 1

            -- Initialize individual ESP setting if not exists
            if individualESPSettings.enemies[enemy.Name] == nil then
                individualESPSettings.enemies[enemy.Name] = true
            end
        end
    end
end

local function scanMapAreas()
    local mapArea = Workspace:FindFirstChild("MapArea")
    if not mapArea then
        warn("MapArea not found in Workspace!")
        return
    end

    local generatedGrids = mapArea:FindFirstChild("GeneratedGrids")
    if not generatedGrids then
        warn("GeneratedGrids not found in MapArea!")
        return
    end

    local activeGrids = generatedGrids:FindFirstChild("ActiveGrids")
    if not activeGrids then
        warn("ActiveGrids not found in GeneratedGrids!")
        return
    end

    local foundMapAreas = 0
    for _, area in pairs(activeGrids:GetChildren()) do
        if not espObjects.mapAreas[area] then
            local espGui = createMapAreaESP(area)
            espObjects.mapAreas[area] = espGui
            foundMapAreas = foundMapAreas + 1

            -- Initialize individual ESP setting if not exists
            if individualESPSettings.mapAreas[area.Name] == nil then
                individualESPSettings.mapAreas[area.Name] = true
            end
        end
    end
end

local function pickupItem(itemName)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("Backpack") then
        warn("Player character or Backpack not found!")
        return false
    end

    local pickup = LocalPlayer.Character.Backpack:FindFirstChild("Pickup")
    if not pickup then
        warn("Pickup RemoteEvent not found in Backpack!")
        return false
    end

    -- Find the item in DraggableFolder
    local draggableFolder = Workspace:FindFirstChild("DraggableFolder")
    if not draggableFolder then
        warn("DraggableFolder not found in Workspace!")
        return false
    end

    local targetItem = nil
    local closestDistance = math.huge

    -- Find the closest item with the matching name
    for _, item in pairs(draggableFolder:GetChildren()) do
        if item.Name == itemName then
            local distance = getDistance(item)
            if distance < closestDistance then
                closestDistance = distance
                targetItem = item
            end
        end
    end

    if not targetItem then
        warn("Item '" .. itemName .. "' not found!")
        return false
    end

    -- Fire the pickup remote
    pickup:FireServer(targetItem)
    return true
end

local function teleportToLocation(locationName)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        warn("Player character or HumanoidRootPart not found!")
        return false
    end

    local location = TELEPORT_LOCATIONS[locationName]
    if not location then
        warn("Unknown location: " .. tostring(locationName))
        return false
    end

    local target = Workspace:FindFirstChild(location.target)
    if not target then
        warn(string.format("%s (%s) not found in Workspace!", location.name, location.target))
        return false
    end

    local targetPosition
    if target:IsA("Model") and target.PrimaryPart then
        targetPosition = target.PrimaryPart.Position
    elseif target:IsA("Part") then
        targetPosition = target.Position
    elseif target:IsA("Model") then
        local part = target:FindFirstChildOfClass("Part")
        if part then
            targetPosition = part.Position
        else
            warn("No valid part found in " .. location.name)
            return false
        end
    else
        warn("Invalid target type for " .. location.name)
        return false
    end

    local offsetPosition = targetPosition + Vector3.new(0, 5, 0)
    LocalPlayer.Character.HumanoidRootPart.CFrame = CFrame.new(offsetPosition)

    return true
end

local function clearAllESP()
    for item, espGui in pairs(espObjects.items) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.items = {}

    for enemy, espGui in pairs(espObjects.enemies) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.enemies = {}

    for mapArea, espGui in pairs(espObjects.mapAreas) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.mapAreas = {}
end

local function toggleItemESP()
    Settings.ItemESP = not Settings.ItemESP
    ESP_CONFIG.Items.Enabled = Settings.ItemESP

    if Settings.ItemESP then
        scanItems()
    end

    updateESPVisibility()

    if GUI and GUI.ItemESPButton then
        GUI.ItemESPButton.Text = "📦 Item ESP: " .. (Settings.ItemESP and "ON" or "OFF")
        GUI.ItemESPButton.BackgroundColor3 = Settings.ItemESP and THEME_GREEN or THEME_GRAY_BLUE
    end
end

local function toggleEnemyESP()
    Settings.EnemyESP = not Settings.EnemyESP
    ESP_CONFIG.Enemies.Enabled = Settings.EnemyESP

    if Settings.EnemyESP then
        scanEnemies()
    end

    updateESPVisibility()

    if GUI and GUI.EnemyESPButton then
        GUI.EnemyESPButton.Text = "👹 Enemy ESP: " .. (Settings.EnemyESP and "ON" or "OFF")
        GUI.EnemyESPButton.BackgroundColor3 = Settings.EnemyESP and THEME_RED or THEME_GRAY_BLUE
    end
end

local function toggleMapAreaESP()
    Settings.MapAreaESP = not Settings.MapAreaESP
    ESP_CONFIG.MapAreas.Enabled = Settings.MapAreaESP

    if Settings.MapAreaESP then
        scanMapAreas()
    end

    updateESPVisibility()

    if GUI and GUI.MapAreaESPButton then
        GUI.MapAreaESPButton.Text = "🗺️ Map Area ESP: " .. (Settings.MapAreaESP and "ON" or "OFF")
        GUI.MapAreaESPButton.BackgroundColor3 = Settings.MapAreaESP and THEME_YELLOW or THEME_GRAY_BLUE
    end
end

local function onItemAdded(item)
    wait(0.1)
    if not espObjects.items[item] then
        local espGui = createItemESP(item)
        espObjects.items[item] = espGui

        -- Initialize individual ESP setting if not exists
        if individualESPSettings.items[item.Name] == nil then
            individualESPSettings.items[item.Name] = true
        end

        -- Refresh the item list
        spawn(function()
            wait(0.5)
            refreshItemList()
        end)
    end
end

local function onEnemyAdded(enemy)
    wait(0.1)
    if not espObjects.enemies[enemy] then
        local espGui = createEnemyESP(enemy)
        espObjects.enemies[enemy] = espGui

        -- Initialize individual ESP setting if not exists
        if individualESPSettings.enemies[enemy.Name] == nil then
            individualESPSettings.enemies[enemy.Name] = true
        end

        -- Refresh the enemy list
        spawn(function()
            wait(0.5)
            refreshEnemyList()
        end)
    end
end

local function onItemRemoved(item)
    if espObjects.items[item] then
        if espObjects.items[item].Parent then
            espObjects.items[item]:Destroy()
        end
        espObjects.items[item] = nil
    end
end

local function onEnemyRemoved(enemy)
    if espObjects.enemies[enemy] then
        if espObjects.enemies[enemy].Parent then
            espObjects.enemies[enemy]:Destroy()
        end
        espObjects.enemies[enemy] = nil
    end
end

local function onMapAreaAdded(mapArea)
    wait(0.1)
    if not espObjects.mapAreas[mapArea] then
        local espGui = createMapAreaESP(mapArea)
        espObjects.mapAreas[mapArea] = espGui

        -- Initialize individual ESP setting if not exists
        if individualESPSettings.mapAreas[mapArea.Name] == nil then
            individualESPSettings.mapAreas[mapArea.Name] = true
        end

        -- Refresh the map area list
        spawn(function()
            wait(0.5)
            refreshMapAreaList()
        end)
    end
end

local function onMapAreaRemoved(mapArea)
    if espObjects.mapAreas[mapArea] then
        if espObjects.mapAreas[mapArea].Parent then
            espObjects.mapAreas[mapArea]:Destroy()
        end
        espObjects.mapAreas[mapArea] = nil
    end
end

local function toggleIndividualItemESP(itemName)
    individualESPSettings.items[itemName] = not individualESPSettings.items[itemName]
    updateESPVisibility()

    -- Update GUI if it exists
    if GUI and GUI.ItemListFrame then
        local button = GUI.ItemListFrame:FindFirstChild("Item_" .. itemName)
        if button then
            local enabled = individualESPSettings.items[itemName]
            button.BackgroundColor3 = enabled and THEME_GREEN or THEME_LIGHT_GRAY
            button.Text = (enabled and "✓ " or "✗ ") .. itemName
        end
    end
end

local function toggleIndividualEnemyESP(enemyName)
    individualESPSettings.enemies[enemyName] = not individualESPSettings.enemies[enemyName]
    updateESPVisibility()

    -- Update GUI if it exists
    if GUI and GUI.EnemyListFrame then
        local button = GUI.EnemyListFrame:FindFirstChild("Enemy_" .. enemyName)
        if button then
            local enabled = individualESPSettings.enemies[enemyName]
            button.BackgroundColor3 = enabled and THEME_RED or THEME_LIGHT_GRAY
            button.Text = (enabled and "✓ " or "✗ ") .. enemyName
        end
    end
end

local function toggleIndividualMapAreaESP(mapAreaName)
    individualESPSettings.mapAreas[mapAreaName] = not individualESPSettings.mapAreas[mapAreaName]
    updateESPVisibility()

    -- Update GUI if it exists
    if GUI and GUI.MapAreaListFrame then
        local button = GUI.MapAreaListFrame:FindFirstChild("MapArea_" .. mapAreaName)
        if button then
            local enabled = individualESPSettings.mapAreas[mapAreaName]
            button.BackgroundColor3 = enabled and THEME_YELLOW or THEME_LIGHT_GRAY
            button.Text = (enabled and "✓ " or "✗ ") .. mapAreaName
        end
    end
end

local function refreshItemList()
    if not GUI or not GUI.ItemListFrame then return end

    -- Clear existing buttons
    for _, child in pairs(GUI.ItemListFrame:GetChildren()) do
        if child.Name:match("^Item_") or child.Name:match("^ItemTP_") or child.Name:match("^ItemPickup_") then
            child:Destroy()
        end
    end

    -- Create buttons for each unique item
    local yOffset = 8 -- Start below the separator line
    local uniqueItems = {}
    for itemName, _ in pairs(individualESPSettings.items) do
        uniqueItems[itemName] = true
    end

    -- Filter items based on search query
    local filteredItems = {}
    for itemName, _ in pairs(uniqueItems) do
        if itemSearchQuery == "" or string.lower(itemName):find(string.lower(itemSearchQuery), 1, true) then
            table.insert(filteredItems, itemName)
        end
    end

    -- Sort items alphabetically (A-Z)
    table.sort(filteredItems, function(a, b)
        return string.lower(a) < string.lower(b)
    end)

    for _, itemName in pairs(filteredItems) do
        local enabled = individualESPSettings.items[itemName]

        -- Main toggle button (adjusted width for 3 buttons)
        local button = CreateElement("TextButton", {
            Name = "Item_" .. itemName,
            BackgroundColor3 = enabled and THEME_GREEN or THEME_LIGHT_GRAY,
            BackgroundTransparency = 0.2,
            BorderSizePixel = 1,
            BorderColor3 = enabled and THEME_GREEN or THEME_LIGHT_GRAY,
            Position = UDim2.new(0, 5, 0, yOffset),
            Size = UDim2.new(1, -95, 0, 28), -- Adjusted width for 3 buttons
            Font = Enum.Font.Gotham,
            Text = (enabled and "✓ " or "✗ ") .. itemName,
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = GUI.ItemListFrame
        })

        CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = button
        })

        -- Pickup button (new button for item pickup)
        local pickupButton = CreateElement("TextButton", {
            Name = "ItemPickup_" .. itemName,
            BackgroundColor3 = Color3.fromRGB(0, 150, 255), -- Blue color for pickup
            BackgroundTransparency = 0.1,
            BorderSizePixel = 1,
            BorderColor3 = Color3.fromRGB(0, 120, 200),
            Position = UDim2.new(1, -90, 0, yOffset),
            Size = UDim2.new(0, 40, 0, 28),
            Font = Enum.Font.GothamBold,
            Text = "GET",
            TextColor3 = THEME_WHITE,
            TextSize = 11,
            Parent = GUI.ItemListFrame
        })

        CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = pickupButton
        })

        -- Teleport button (repositioned for 3 buttons)
        local teleportButton = CreateElement("TextButton", {
            Name = "ItemTP_" .. itemName,
            BackgroundColor3 = THEME_YELLOW,
            BackgroundTransparency = 0.1,
            BorderSizePixel = 1,
            BorderColor3 = THEME_ORANGE,
            Position = UDim2.new(1, -45, 0, yOffset),
            Size = UDim2.new(0, 40, 0, 28),
            Font = Enum.Font.GothamBold,
            Text = "TP",
            TextColor3 = THEME_BLACK,
            TextSize = 12,
            Parent = GUI.ItemListFrame
        })

        CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = teleportButton
        })

        button.MouseButton1Click:Connect(function()
            toggleIndividualItemESP(itemName)
        end)

        pickupButton.MouseButton1Click:Connect(function()
            -- Pickup the item using the remote
            local success = pickupItem(itemName)
            if success then
                -- Visual feedback
                pickupButton.BackgroundColor3 = THEME_GREEN
                pickupButton.Text = "✓"
                print("✓ Picked up: " .. itemName)

                -- Reset button after short delay
                spawn(function()
                    wait(0.5)
                    pickupButton.BackgroundColor3 = Color3.fromRGB(0, 150, 255)
                    pickupButton.Text = "GET"
                end)
            else
                -- Error feedback
                pickupButton.BackgroundColor3 = THEME_RED
                pickupButton.Text = "✗"
                warn("✗ Failed to pickup: " .. itemName)

                -- Reset button after short delay
                spawn(function()
                    wait(0.5)
                    pickupButton.BackgroundColor3 = Color3.fromRGB(0, 150, 255)
                    pickupButton.Text = "GET"
                end)
            end
        end)

        teleportButton.MouseButton1Click:Connect(function()
            -- Find the actual item object and teleport to it (find closest if multiple)
            local draggableFolder = game.Workspace:FindFirstChild("DraggableFolder")
            if draggableFolder then
                local closestItem = nil
                local closestDistance = math.huge

                for _, item in pairs(draggableFolder:GetChildren()) do
                    if item.Name == itemName then
                        local distance = getDistance(item)
                        if distance < closestDistance then
                            closestDistance = distance
                            closestItem = item
                        end
                    end
                end

                if closestItem then
                    -- Visual feedback
                    teleportButton.BackgroundColor3 = THEME_GREEN
                    teleportButton.Text = "✓"
                    teleportToObject(closestItem)

                    -- Reset button after short delay
                    spawn(function()
                        wait(0.5)
                        teleportButton.BackgroundColor3 = THEME_YELLOW
                        teleportButton.Text = "TP"
                    end)
                else
                    -- Error feedback
                    teleportButton.BackgroundColor3 = THEME_RED
                    teleportButton.Text = "✗"
                    warn("Item '" .. itemName .. "' not found!")

                    -- Reset button after short delay
                    spawn(function()
                        wait(0.5)
                        teleportButton.BackgroundColor3 = THEME_YELLOW
                        teleportButton.Text = "TP"
                    end)
                end
            end
        end)

        -- Add hover effects for main button
        button.MouseEnter:Connect(function()
            button.BackgroundTransparency = 0.1
        end)

        button.MouseLeave:Connect(function()
            button.BackgroundTransparency = 0.3
        end)

        -- Add hover effects for pickup button
        pickupButton.MouseEnter:Connect(function()
            pickupButton.BackgroundTransparency = 0.05
            pickupButton.BackgroundColor3 = Color3.fromRGB(0, 120, 200)
        end)

        pickupButton.MouseLeave:Connect(function()
            pickupButton.BackgroundTransparency = 0.2
            pickupButton.BackgroundColor3 = Color3.fromRGB(0, 150, 255)
        end)

        -- Add hover effects for teleport button
        teleportButton.MouseEnter:Connect(function()
            teleportButton.BackgroundTransparency = 0.05
            teleportButton.BackgroundColor3 = THEME_ORANGE
        end)

        teleportButton.MouseLeave:Connect(function()
            teleportButton.BackgroundTransparency = 0.2
            teleportButton.BackgroundColor3 = THEME_YELLOW
        end)

        yOffset = yOffset + 33 -- Better spacing between items
    end

    -- AutomaticCanvasSize will handle the canvas size automatically
end

local function refreshEnemyList()
    if not GUI or not GUI.EnemyListFrame then return end

    -- Clear existing buttons
    for _, child in pairs(GUI.EnemyListFrame:GetChildren()) do
        if child.Name:match("^Enemy_") or child.Name:match("^EnemyTP_") then
            child:Destroy()
        end
    end

    -- Create buttons for each unique enemy
    local yOffset = 8 -- Start below the separator line
    local uniqueEnemies = {}
    for enemyName, _ in pairs(individualESPSettings.enemies) do
        uniqueEnemies[enemyName] = true
    end

    -- Sort enemies alphabetically
    local sortedEnemies = {}
    for enemyName, _ in pairs(uniqueEnemies) do
        table.insert(sortedEnemies, enemyName)
    end
    table.sort(sortedEnemies)

    for _, enemyName in pairs(sortedEnemies) do
        local enabled = individualESPSettings.enemies[enemyName]

        -- Main toggle button (better spacing and colors)
        local button = CreateElement("TextButton", {
            Name = "Enemy_" .. enemyName,
            BackgroundColor3 = enabled and THEME_RED or THEME_LIGHT_GRAY,
            BackgroundTransparency = 0.2,
            BorderSizePixel = 1,
            BorderColor3 = enabled and THEME_RED or THEME_LIGHT_GRAY,
            Position = UDim2.new(0, 5, 0, yOffset),
            Size = UDim2.new(1, -55, 0, 28), -- Better width and height
            Font = Enum.Font.Gotham,
            Text = (enabled and "✓ " or "✗ ") .. enemyName,
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = GUI.EnemyListFrame
        })

        CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = button
        })

        -- Teleport button (better positioning and colors)
        local teleportButton = CreateElement("TextButton", {
            Name = "EnemyTP_" .. enemyName,
            BackgroundColor3 = THEME_ORANGE,
            BackgroundTransparency = 0.1,
            BorderSizePixel = 1,
            BorderColor3 = THEME_RED,
            Position = UDim2.new(1, -45, 0, yOffset),
            Size = UDim2.new(0, 40, 0, 28), -- Better size matching main button
            Font = Enum.Font.GothamBold,
            Text = "TP",
            TextColor3 = THEME_BLACK,
            TextSize = 12,
            Parent = GUI.EnemyListFrame
        })

        CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = teleportButton
        })

        button.MouseButton1Click:Connect(function()
            toggleIndividualEnemyESP(enemyName)
        end)

        teleportButton.MouseButton1Click:Connect(function()
            -- Find the actual enemy object and teleport to it (find closest if multiple)
            local enemiesFolder = game.Workspace:FindFirstChild("Enemies")
            if enemiesFolder then
                local closestEnemy = nil
                local closestDistance = math.huge

                for _, enemy in pairs(enemiesFolder:GetChildren()) do
                    if enemy.Name == enemyName then
                        local distance = getDistance(enemy)
                        if distance < closestDistance then
                            closestDistance = distance
                            closestEnemy = enemy
                        end
                    end
                end

                if closestEnemy then
                    -- Visual feedback
                    teleportButton.BackgroundColor3 = THEME_GREEN
                    teleportButton.Text = "✓"
                    teleportToObject(closestEnemy)

                    -- Reset button after short delay
                    spawn(function()
                        wait(0.5)
                        teleportButton.BackgroundColor3 = THEME_ORANGE
                        teleportButton.Text = "TP"
                    end)
                else
                    -- Error feedback
                    teleportButton.BackgroundColor3 = THEME_RED
                    teleportButton.Text = "✗"
                    warn("Enemy '" .. enemyName .. "' not found!")

                    -- Reset button after short delay
                    spawn(function()
                        wait(0.5)
                        teleportButton.BackgroundColor3 = THEME_ORANGE
                        teleportButton.Text = "TP"
                    end)
                end
            end
        end)

        -- Add hover effects for main button
        button.MouseEnter:Connect(function()
            button.BackgroundTransparency = 0.1
        end)

        button.MouseLeave:Connect(function()
            button.BackgroundTransparency = 0.3
        end)

        -- Add hover effects for teleport button
        teleportButton.MouseEnter:Connect(function()
            teleportButton.BackgroundTransparency = 0.05
            teleportButton.BackgroundColor3 = THEME_RED
        end)

        teleportButton.MouseLeave:Connect(function()
            teleportButton.BackgroundTransparency = 0.2
            teleportButton.BackgroundColor3 = THEME_ORANGE
        end)

        yOffset = yOffset + 33 -- Better spacing between items
    end

    -- AutomaticCanvasSize will handle the canvas size automatically
end

local function refreshMapAreaList()
    if not GUI or not GUI.MapAreaListFrame then return end

    -- Clear existing buttons
    for _, child in pairs(GUI.MapAreaListFrame:GetChildren()) do
        if child.Name:match("^MapArea_") or child.Name:match("^MapAreaTP_") then
            child:Destroy()
        end
    end

    -- Create buttons for each unique map area
    local yOffset = 8 -- Start below the separator line
    local uniqueMapAreas = {}
    for mapAreaName, _ in pairs(individualESPSettings.mapAreas) do
        uniqueMapAreas[mapAreaName] = true
    end

    -- Sort map areas alphabetically
    local sortedMapAreas = {}
    for mapAreaName, _ in pairs(uniqueMapAreas) do
        table.insert(sortedMapAreas, mapAreaName)
    end
    table.sort(sortedMapAreas)

    for _, mapAreaName in pairs(sortedMapAreas) do
        local enabled = individualESPSettings.mapAreas[mapAreaName]

        -- Main toggle button (better spacing and colors)
        local button = CreateElement("TextButton", {
            Name = "MapArea_" .. mapAreaName,
            BackgroundColor3 = enabled and THEME_YELLOW or THEME_LIGHT_GRAY,
            BackgroundTransparency = 0.2,
            BorderSizePixel = 1,
            BorderColor3 = enabled and THEME_YELLOW or THEME_LIGHT_GRAY,
            Position = UDim2.new(0, 5, 0, yOffset),
            Size = UDim2.new(1, -55, 0, 28), -- Better width and height
            Font = Enum.Font.Gotham,
            Text = (enabled and "✓ " or "✗ ") .. mapAreaName,
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = GUI.MapAreaListFrame
        })

        CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = button
        })

        -- Teleport button (better positioning and colors)
        local teleportButton = CreateElement("TextButton", {
            Name = "MapAreaTP_" .. mapAreaName,
            BackgroundColor3 = THEME_ORANGE,
            BackgroundTransparency = 0.1,
            BorderSizePixel = 1,
            BorderColor3 = THEME_YELLOW,
            Position = UDim2.new(1, -45, 0, yOffset),
            Size = UDim2.new(0, 40, 0, 28), -- Better size matching main button
            Font = Enum.Font.GothamBold,
            Text = "TP",
            TextColor3 = THEME_BLACK,
            TextSize = 12,
            Parent = GUI.MapAreaListFrame
        })

        CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = teleportButton
        })

        button.MouseButton1Click:Connect(function()
            toggleIndividualMapAreaESP(mapAreaName)
        end)

        teleportButton.MouseButton1Click:Connect(function()
            -- Find the actual map area object and teleport to it (find closest if multiple)
            local mapArea = game.Workspace:FindFirstChild("MapArea")
            if mapArea then
                local generatedGrids = mapArea:FindFirstChild("GeneratedGrids")
                if generatedGrids then
                    local activeGrids = generatedGrids:FindFirstChild("ActiveGrids")
                    if activeGrids then
                        local closestMapArea = nil
                        local closestDistance = math.huge

                        for _, area in pairs(activeGrids:GetChildren()) do
                            if area.Name == mapAreaName then
                                local distance = getDistance(area)
                                if distance < closestDistance then
                                    closestDistance = distance
                                    closestMapArea = area
                                end
                            end
                        end

                        if closestMapArea then
                            -- Visual feedback
                            teleportButton.BackgroundColor3 = THEME_GREEN
                            teleportButton.Text = "✓"
                            teleportToObject(closestMapArea)

                            -- Reset button after short delay
                            spawn(function()
                                wait(0.5)
                                teleportButton.BackgroundColor3 = THEME_ORANGE
                                teleportButton.Text = "TP"
                            end)
                        else
                            -- Error feedback
                            teleportButton.BackgroundColor3 = THEME_RED
                            teleportButton.Text = "✗"
                            warn("Map Area '" .. mapAreaName .. "' not found!")

                            -- Reset button after short delay
                            spawn(function()
                                wait(0.5)
                                teleportButton.BackgroundColor3 = THEME_ORANGE
                                teleportButton.Text = "TP"
                            end)
                        end
                    end
                end
            end
        end)

        -- Add hover effects for main button
        button.MouseEnter:Connect(function()
            button.BackgroundTransparency = 0.1
        end)

        button.MouseLeave:Connect(function()
            button.BackgroundTransparency = 0.3
        end)

        -- Add hover effects for teleport button
        teleportButton.MouseEnter:Connect(function()
            teleportButton.BackgroundTransparency = 0.05
            teleportButton.BackgroundColor3 = THEME_YELLOW
        end)

        teleportButton.MouseLeave:Connect(function()
            teleportButton.BackgroundTransparency = 0.2
            teleportButton.BackgroundColor3 = THEME_ORANGE
        end)

        yOffset = yOffset + 33 -- Better spacing between items
    end

    -- AutomaticCanvasSize will handle the canvas size automatically
end

local function toggleGUIVisibility()
    Settings.GUIVisible = not Settings.GUIVisible

    if GUI and GUI.ScreenGui then
        GUI.ScreenGui.Enabled = Settings.GUIVisible
    end
end

local function onCharacterAdded(character)
    wait(1)
end
local function updateStatus()
    if not GUI or not GUI.StatusLabel then
        return
    end

    local itemCount = 0
    local enemyCount = 0
    local mapAreaCount = 0

    for _ in pairs(espObjects.items) do
        itemCount = itemCount + 1
    end

    for _ in pairs(espObjects.enemies) do
        enemyCount = enemyCount + 1
    end

    for _ in pairs(espObjects.mapAreas) do
        mapAreaCount = mapAreaCount + 1
    end

    GUI.StatusLabel.Text =
        string.format(
        "Status: Active\nItems: %d | Enemies: %d | Map Areas: %d\nItem Range: %d | Enemy Range: %d | Map Range: %d",
        itemCount,
        enemyCount,
        mapAreaCount,
        ESP_CONFIG.Items.MaxDistance,
        ESP_CONFIG.Enemies.MaxDistance,
        ESP_CONFIG.MapAreas.MaxDistance
    )
end

local function CreateGUI()
    local ScreenGui =
        CreateElement(
        "ScreenGui",
        {
            Name = "DeadShelterGUI",
            ResetOnSpawn = false,
            ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
            Parent = PlayerGui
        }
    )

    local MainFrame =
        CreateElement(
        "Frame",
        {
            Name = "MainFrame",
            AnchorPoint = Vector2.new(0.5, 0.5),
            BackgroundColor3 = THEME_BLACK,
            BackgroundTransparency = Settings.Transparency,
            BorderSizePixel = 0,
            Position = UDim2.new(0.5, 0, 0.5, 0),
            Size = Settings.UISize,
            Parent = ScreenGui
        }
    )

    local UICorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 10),
            Parent = MainFrame
        }
    )

    local TitleBar =
        CreateElement(
        "Frame",
        {
            Name = "TitleBar",
            BackgroundColor3 = THEME_RED,
            BackgroundTransparency = 0.1,
            BorderSizePixel = 0,
            Size = UDim2.new(1, 0, 0, 40),
            Parent = MainFrame
        }
    )

    local TitleCorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 10),
            Parent = TitleBar
        }
    )

    local BottomFrame =
        CreateElement(
        "Frame",
        {
            BackgroundColor3 = THEME_RED,
            BackgroundTransparency = 0.1,
            BorderSizePixel = 0,
            Position = UDim2.new(0, 0, 0.5, 0),
            Size = UDim2.new(1, 0, 0.5, 0),
            Parent = TitleBar
        }
    )

    local TitleText =
        CreateElement(
        "TextLabel",
        {
            Name = "TitleText",
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 15, 0, 0),
            Size = UDim2.new(1, -120, 1, 0),
            Font = Enum.Font.GothamBold,
            Text = "Dead Shelter",
            TextColor3 = THEME_WHITE,
            TextSize = 18,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = TitleBar
        }
    )

    local ControlsFrame =
        CreateElement(
        "Frame",
        {
            Name = "ControlsFrame",
            AnchorPoint = Vector2.new(1, 0.5),
            BackgroundTransparency = 1,
            Position = UDim2.new(1, -10, 0.5, 0),
            Size = UDim2.new(0, 90, 0, 24),
            Parent = TitleBar
        }
    )

    local MinimizeButton =
        CreateElement(
        "TextButton",
        {
            Name = "MinimizeButton",
            AnchorPoint = Vector2.new(1, 0.5),
            BackgroundTransparency = 1,
            Position = UDim2.new(1, -50, 0.5, 0),
            Size = UDim2.new(0, 24, 0, 24),
            Font = Enum.Font.GothamBold,
            Text = "-",
            TextColor3 = THEME_WHITE,
            TextSize = 24,
            Parent = ControlsFrame
        }
    )

    local CloseButton =
        CreateElement(
        "TextButton",
        {
            Name = "CloseButton",
            AnchorPoint = Vector2.new(1, 0.5),
            BackgroundTransparency = 1,
            Position = UDim2.new(1, 0, 0.5, 0),
            Size = UDim2.new(0, 24, 0, 24),
            Font = Enum.Font.GothamBold,
            Text = "×",
            TextColor3 = THEME_WHITE,
            TextSize = 24,
            Parent = ControlsFrame
        }
    )

    local ContentArea =
        CreateElement(
        "ScrollingFrame",
        {
            Name = "ContentArea",
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 15, 0, 50),
            Size = UDim2.new(1, -30, 1, -65),
            CanvasSize = UDim2.new(0, 0, 0, 800), -- Increased canvas size for new content
            ScrollBarThickness = 10,
            ScrollBarImageColor3 = THEME_WHITE, -- White color for main scrollbar
            ScrollBarImageTransparency = 0.1,
            BorderSizePixel = 0,
            TopImage = "rbxasset://textures/ui/Scroll/scroll-top.png",
            BottomImage = "rbxasset://textures/ui/Scroll/scroll-bottom.png",
            MidImage = "rbxasset://textures/ui/Scroll/scroll-middle.png",
            ScrollingDirection = Enum.ScrollingDirection.Y,
            VerticalScrollBarInset = Enum.ScrollBarInset.Always,
            ElasticBehavior = Enum.ElasticBehavior.Never,
            ScrollingEnabled = true,
            ClipsDescendants = true,
            Parent = MainFrame
        }
    )

    local function createButton(text, position, size, color, callback)
        local button =
            CreateElement(
            "TextButton",
            {
                BackgroundColor3 = color or THEME_GRAY_BLUE,
                BackgroundTransparency = 0.3,
                BorderSizePixel = 0,
                Position = position,
                Size = size or UDim2.new(1, 0, 0, 35),
                Font = Enum.Font.GothamSemibold,
                Text = text,
                TextColor3 = THEME_WHITE,
                TextSize = 14,
                Parent = ContentArea
            }
        )

        local buttonCorner =
            CreateElement(
            "UICorner",
            {
                CornerRadius = UDim.new(0, 6),
                Parent = button
            }
        )

        if callback then
            button.MouseButton1Click:Connect(callback)
        end

        return button
    end

    local espLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 0),
            Size = UDim2.new(1, 0, 0, 25),
            Font = Enum.Font.GothamBold,
            Text = "ESP Controls",
            TextColor3 = THEME_WHITE,
            TextSize = 16,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    local itemESPButton =
        createButton(
        "📦 Item ESP: OFF",
        UDim2.new(0, 0, 0, 30),
        UDim2.new(0.48, 0, 0, 35),
        THEME_GRAY_BLUE,
        toggleItemESP
    )
    local enemyESPButton =
        createButton(
        "👹 Enemy ESP: OFF",
        UDim2.new(0.52, 0, 0, 30),
        UDim2.new(0.48, 0, 0, 35),
        THEME_GRAY_BLUE,
        toggleEnemyESP
    )

    local mapAreaESPButton =
        createButton(
        "🗺️ Map Area ESP: OFF",
        UDim2.new(0, 0, 0, 70),
        UDim2.new(1, 0, 0, 35),
        THEME_GRAY_BLUE,
        toggleMapAreaESP
    )

    local itemRangeLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 115),
            Size = UDim2.new(0.48, 0, 0, 20),
            Font = Enum.Font.Gotham,
            Text = "Item Range: " .. ESP_CONFIG.Items.MaxDistance,
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    local itemRangeSlider =
        CreateElement(
        "Frame",
        {
            BackgroundColor3 = THEME_DARK_GRAY,
            BorderSizePixel = 0,
            Position = UDim2.new(0, 0, 0, 135),
            Size = UDim2.new(0.48, 0, 0, 20),
            Parent = ContentArea
        }
    )

    local itemRangeCorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 10),
            Parent = itemRangeSlider
        }
    )

    local itemRangeButton =
        CreateElement(
        "TextButton",
        {
            BackgroundColor3 = THEME_GREEN,
            BorderSizePixel = 0,
            Position = UDim2.new(ESP_CONFIG.Items.MaxDistance / 500, -10, 0, 2),
            Size = UDim2.new(0, 20, 0, 16),
            Text = "",
            Parent = itemRangeSlider
        }
    )

    local itemRangeButtonCorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 8),
            Parent = itemRangeButton
        }
    )

    local enemyRangeLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0.52, 0, 0, 115),
            Size = UDim2.new(0.48, 0, 0, 20),
            Font = Enum.Font.Gotham,
            Text = "Enemy Range: " .. ESP_CONFIG.Enemies.MaxDistance,
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    local enemyRangeSlider =
        CreateElement(
        "Frame",
        {
            BackgroundColor3 = THEME_DARK_GRAY,
            BorderSizePixel = 0,
            Position = UDim2.new(0.52, 0, 0, 135),
            Size = UDim2.new(0.48, 0, 0, 20),
            Parent = ContentArea
        }
    )

    local enemyRangeCorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 10),
            Parent = enemyRangeSlider
        }
    )

    local enemyRangeButton =
        CreateElement(
        "TextButton",
        {
            BackgroundColor3 = THEME_RED,
            BorderSizePixel = 0,
            Position = UDim2.new(ESP_CONFIG.Enemies.MaxDistance / 200, -10, 0, 2),
            Size = UDim2.new(0, 20, 0, 16),
            Text = "",
            Parent = enemyRangeSlider
        }
    )

    local enemyRangeButtonCorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 8),
            Parent = enemyRangeButton
        }
    )

    local itemDragging = false
    itemRangeButton.InputBegan:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                itemDragging = true
            end
        end
    )

    itemRangeButton.InputEnded:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                itemDragging = false
            end
        end
    )

    UserInputService.InputChanged:Connect(
        function(input)
            if itemDragging and input.UserInputType == Enum.UserInputType.MouseMovement then
                local sliderPosition = itemRangeSlider.AbsolutePosition.X
                local sliderSize = itemRangeSlider.AbsoluteSize.X
                local mouseX = input.Position.X

                local relativeX = math.clamp((mouseX - sliderPosition) / sliderSize, 0, 1)
                itemRangeButton.Position = UDim2.new(relativeX, -10, 0, 2)

                ESP_CONFIG.Items.MaxDistance = math.floor(10 + (relativeX * 490))
                itemRangeLabel.Text = "Item Range: " .. ESP_CONFIG.Items.MaxDistance
            end
        end
    )

    local enemyDragging = false
    enemyRangeButton.InputBegan:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                enemyDragging = true
            end
        end
    )

    enemyRangeButton.InputEnded:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                enemyDragging = false
            end
        end
    )

    UserInputService.InputChanged:Connect(
        function(input)
            if enemyDragging and input.UserInputType == Enum.UserInputType.MouseMovement then
                local sliderPosition = enemyRangeSlider.AbsolutePosition.X
                local sliderSize = enemyRangeSlider.AbsoluteSize.X
                local mouseX = input.Position.X

                local relativeX = math.clamp((mouseX - sliderPosition) / sliderSize, 0, 1)
                enemyRangeButton.Position = UDim2.new(relativeX, -10, 0, 2)

                ESP_CONFIG.Enemies.MaxDistance = math.floor(10 + (relativeX * 490))
                enemyRangeLabel.Text = "Enemy Range: " .. ESP_CONFIG.Enemies.MaxDistance
            end
        end
    )

    -- Map Area Range Controls
    local mapAreaRangeLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 200),
            Size = UDim2.new(1, 0, 0, 20),
            Font = Enum.Font.Gotham,
            Text = "Map Area Range: " .. ESP_CONFIG.MapAreas.MaxDistance,
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    local mapAreaRangeSlider =
        CreateElement(
        "Frame",
        {
            BackgroundColor3 = THEME_DARK_GRAY,
            BorderSizePixel = 0,
            Position = UDim2.new(0, 0, 0, 220),
            Size = UDim2.new(1, 0, 0, 20),
            Parent = ContentArea
        }
    )

    local mapAreaRangeCorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 10),
            Parent = mapAreaRangeSlider
        }
    )

    local mapAreaRangeButton =
        CreateElement(
        "TextButton",
        {
            BackgroundColor3 = THEME_YELLOW,
            BorderSizePixel = 0,
            Position = UDim2.new(ESP_CONFIG.MapAreas.MaxDistance / 1000, -10, 0, 2),
            Size = UDim2.new(0, 20, 0, 16),
            Text = "",
            Parent = mapAreaRangeSlider
        }
    )

    local mapAreaRangeButtonCorner =
        CreateElement(
        "UICorner",
        {
            CornerRadius = UDim.new(0, 8),
            Parent = mapAreaRangeButton
        }
    )

    local mapAreaDragging = false
    mapAreaRangeButton.InputBegan:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                mapAreaDragging = true
            end
        end
    )

    mapAreaRangeButton.InputEnded:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                mapAreaDragging = false
            end
        end
    )

    UserInputService.InputChanged:Connect(
        function(input)
            if mapAreaDragging and input.UserInputType == Enum.UserInputType.MouseMovement then
                local sliderPosition = mapAreaRangeSlider.AbsolutePosition.X
                local sliderSize = mapAreaRangeSlider.AbsoluteSize.X
                local mouseX = input.Position.X

                local relativeX = math.clamp((mouseX - sliderPosition) / sliderSize, 0, 1)
                mapAreaRangeButton.Position = UDim2.new(relativeX, -10, 0, 2)

                ESP_CONFIG.MapAreas.MaxDistance = math.floor(50 + (relativeX * 950))
                mapAreaRangeLabel.Text = "Map Area Range: " .. ESP_CONFIG.MapAreas.MaxDistance
            end
        end
    )

    local teleportLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 250),
            Size = UDim2.new(1, 0, 0, 25),
            Font = Enum.Font.GothamBold,
            Text = "Teleport Controls",
            TextColor3 = THEME_WHITE,
            TextSize = 16,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    local teleportBaseButton =
        createButton(
        "🏠 Teleport to Base",
        UDim2.new(0, 0, 0, 280),
        UDim2.new(0.48, 0, 0, 35),
        THEME_GREEN,
        function()
            teleportToLocation("Base")
        end
    )

    local teleportPawnshopButton =
        createButton(
        "🏪 Teleport to Pawnshop",
        UDim2.new(0.52, 0, 0, 280),
        UDim2.new(0.48, 0, 0, 35),
        THEME_YELLOW,
        function()
            teleportToLocation("Pawnshop")
        end
    )

    local statusLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 330),
            Size = UDim2.new(1, 0, 0, 60),
            Font = Enum.Font.Gotham,
            Text = "Status: Ready\nItems: 0 | Enemies: 0 | Map Areas: 0\nItem Range: " ..
                ESP_CONFIG.Items.MaxDistance .. " | Enemy Range: " .. ESP_CONFIG.Enemies.MaxDistance .. " | Map Range: " .. ESP_CONFIG.MapAreas.MaxDistance,
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    -- Item List Section
    local itemListLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 400),
            Size = UDim2.new(1, 0, 0, 25),
            Font = Enum.Font.GothamBold,
            Text = "📦 Item ESP List (Click to toggle)",
            TextColor3 = THEME_WHITE,
            TextSize = 14,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    -- Item Search Box
    local itemSearchBox =
        CreateElement(
        "TextBox",
        {
            Name = "ItemSearchBox",
            BackgroundColor3 = THEME_DARK_GRAY,
            BackgroundTransparency = 0.2,
            BorderSizePixel = 1,
            BorderColor3 = THEME_GREEN,
            Position = UDim2.new(0, 0, 0, 430),
            Size = UDim2.new(1, 0, 0, 25),
            Font = Enum.Font.Gotham,
            PlaceholderText = "🔍 Search items...",
            PlaceholderColor3 = Color3.fromRGB(150, 150, 150),
            Text = "",
            TextColor3 = THEME_WHITE,
            TextSize = 12,
            TextXAlignment = Enum.TextXAlignment.Left,
            ClearTextOnFocus = false,
            Parent = ContentArea
        }
    )

    CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 4),
        Parent = itemSearchBox
    })

    -- Search box event handler
    itemSearchBox:GetPropertyChangedSignal("Text"):Connect(function()
        itemSearchQuery = itemSearchBox.Text
        refreshItemList()
    end)

    local itemListFrame =
        CreateElement(
        "ScrollingFrame",
        {
            Name = "ItemListFrame",
            BackgroundColor3 = THEME_DARK_GRAY,
            BackgroundTransparency = 0.3,
            BorderSizePixel = 1,
            BorderColor3 = THEME_GREEN,
            Position = UDim2.new(0, 0, 0, 460),
            Size = UDim2.new(1, 0, 0, 120),
            CanvasSize = UDim2.new(0, 0, 0, 0),
            ScrollBarThickness = 12,
            ScrollBarImageColor3 = THEME_GREEN,
            ScrollBarImageTransparency = 0.1,
            ScrollingDirection = Enum.ScrollingDirection.Y,
            VerticalScrollBarInset = Enum.ScrollBarInset.Always,
            ElasticBehavior = Enum.ElasticBehavior.Never,
            ScrollingEnabled = true,
            ClipsDescendants = true,
            AutomaticCanvasSize = Enum.AutomaticSize.Y,
            Parent = ContentArea
        }
    )

    CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 6),
        Parent = itemListFrame
    })

    -- Add a visual separator line at the top of item list
    CreateElement("Frame", {
        BackgroundColor3 = THEME_GREEN,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(1, 0, 0, 2),
        Parent = itemListFrame
    })

    -- Enemy List Section
    local enemyListLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 590),
            Size = UDim2.new(1, 0, 0, 25),
            Font = Enum.Font.GothamBold,
            Text = "👹 Enemy ESP List (Click to toggle)",
            TextColor3 = THEME_WHITE,
            TextSize = 14,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    local enemyListFrame =
        CreateElement(
        "ScrollingFrame",
        {
            Name = "EnemyListFrame",
            BackgroundColor3 = THEME_DARK_GRAY,
            BackgroundTransparency = 0.3,
            BorderSizePixel = 1,
            BorderColor3 = THEME_RED,
            Position = UDim2.new(0, 0, 0, 620),
            Size = UDim2.new(1, 0, 0, 120),
            CanvasSize = UDim2.new(0, 0, 0, 0),
            ScrollBarThickness = 12,
            ScrollBarImageColor3 = THEME_RED,
            ScrollBarImageTransparency = 0.1,
            ScrollingDirection = Enum.ScrollingDirection.Y,
            VerticalScrollBarInset = Enum.ScrollBarInset.Always,
            ElasticBehavior = Enum.ElasticBehavior.Never,
            ScrollingEnabled = true,
            ClipsDescendants = true,
            AutomaticCanvasSize = Enum.AutomaticSize.Y,
            Parent = ContentArea
        }
    )

    CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 6),
        Parent = enemyListFrame
    })

    -- Add a visual separator line at the top of enemy list
    CreateElement("Frame", {
        BackgroundColor3 = THEME_RED,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(1, 0, 0, 2),
        Parent = enemyListFrame
    })

    -- Map Area List Section
    local mapAreaListLabel =
        CreateElement(
        "TextLabel",
        {
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 0, 0, 750),
            Size = UDim2.new(1, 0, 0, 25),
            Font = Enum.Font.GothamBold,
            Text = "🗺️ Map Area ESP List (Click to toggle)",
            TextColor3 = THEME_WHITE,
            TextSize = 14,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = ContentArea
        }
    )

    local mapAreaListFrame =
        CreateElement(
        "ScrollingFrame",
        {
            Name = "MapAreaListFrame",
            BackgroundColor3 = THEME_DARK_GRAY,
            BackgroundTransparency = 0.3,
            BorderSizePixel = 1,
            BorderColor3 = THEME_YELLOW,
            Position = UDim2.new(0, 0, 0, 780),
            Size = UDim2.new(1, 0, 0, 120),
            CanvasSize = UDim2.new(0, 0, 0, 0),
            ScrollBarThickness = 12,
            ScrollBarImageColor3 = THEME_YELLOW,
            ScrollBarImageTransparency = 0.1,
            ScrollingDirection = Enum.ScrollingDirection.Y,
            VerticalScrollBarInset = Enum.ScrollBarInset.Always,
            ElasticBehavior = Enum.ElasticBehavior.Never,
            ScrollingEnabled = true,
            ClipsDescendants = true,
            AutomaticCanvasSize = Enum.AutomaticSize.Y,
            Parent = ContentArea
        }
    )

    CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 6),
        Parent = mapAreaListFrame
    })

    -- Add a visual separator line at the top of map area list
    CreateElement("Frame", {
        BackgroundColor3 = THEME_YELLOW,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(1, 0, 0, 2),
        Parent = mapAreaListFrame
    })

    local function updateCanvasSize()
        local maxY = 0
        for _, child in pairs(ContentArea:GetChildren()) do
            if child:IsA("GuiObject") then
                local childBottom = child.Position.Y.Offset + child.Size.Y.Offset
                if childBottom > maxY then
                    maxY = childBottom
                end
            end
        end

        local newCanvasSize = maxY + 30
        ContentArea.CanvasSize = UDim2.new(0, 0, 0, newCanvasSize)

        local frameHeight = ContentArea.AbsoluteSize.Y
        if newCanvasSize > frameHeight then
            ContentArea.ScrollBarImageTransparency = 0.2
        else
            ContentArea.ScrollBarImageTransparency = 0.8
        end
    end

    spawn(
        function()
            wait(0.1)
            updateCanvasSize()
        end
    )

    ContentArea.ChildAdded:Connect(
        function()
            wait(0.1)
            updateCanvasSize()
        end
    )

    ContentArea.ChildRemoved:Connect(
        function()
            wait(0.1)
            updateCanvasSize()
        end
    )

    ContentArea:GetPropertyChangedSignal("AbsoluteSize"):Connect(updateCanvasSize)

    return {
        ScreenGui = ScreenGui,
        MainFrame = MainFrame,
        TitleBar = TitleBar,
        MinimizeButton = MinimizeButton,
        CloseButton = CloseButton,
        ContentArea = ContentArea,
        StatusLabel = statusLabel,
        ItemESPButton = itemESPButton,
        EnemyESPButton = enemyESPButton,
        MapAreaESPButton = mapAreaESPButton,
        ItemRangeLabel = itemRangeLabel,
        EnemyRangeLabel = enemyRangeLabel,
        MapAreaRangeLabel = mapAreaRangeLabel,
        ItemSearchBox = itemSearchBox,
        ItemListFrame = itemListFrame,
        EnemyListFrame = enemyListFrame,
        MapAreaListFrame = mapAreaListFrame
    }
end

local function StartDeadShelterScript()
    GUI = CreateGUI()
    local Dragging = false
    local DragInput
    local DragStart
    local StartPos

    local function UpdateDrag()
        if not DragInput then
            return
        end
        local Delta = DragInput.Position - DragStart
        local Position =
            UDim2.new(StartPos.X.Scale, StartPos.X.Offset + Delta.X, StartPos.Y.Scale, StartPos.Y.Offset + Delta.Y)
        TweenService:Create(GUI.MainFrame, TweenInfo.new(Settings.DragSpeed), {Position = Position}):Play()
    end

    GUI.TitleBar.InputBegan:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                Dragging = true
                DragStart = input.Position
                StartPos = GUI.MainFrame.Position
                input.Changed:Connect(
                    function()
                        if input.UserInputState == Enum.UserInputState.End then
                            Dragging = false
                            DragInput = nil
                        end
                    end
                )
            end
        end
    )

    UserInputService.InputChanged:Connect(
        function(input)
            if input.UserInputType == Enum.UserInputType.MouseMovement and Dragging then
                DragInput = input
                UpdateDrag()
            end
        end
    )

    GUI.CloseButton.MouseButton1Click:Connect(
        function()
            GUI.ScreenGui:Destroy()
            clearAllESP()
        end
    )

    GUI.MinimizeButton.MouseButton1Click:Connect(
        function()
            Settings.MinimizedState = not Settings.MinimizedState
            if Settings.MinimizedState then
                GUI.ContentArea.Visible = false
                TweenService:Create(
                    GUI.MainFrame,
                    TweenInfo.new(0.3),
                    {
                        Size = UDim2.new(0, Settings.UISize.X.Offset, 0, 40)
                    }
                ):Play()
                GUI.MinimizeButton.Text = "+"
            else
                GUI.ContentArea.Visible = true
                TweenService:Create(
                    GUI.MainFrame,
                    TweenInfo.new(0.3),
                    {
                        Size = Settings.UISize
                    }
                ):Play()
                GUI.MinimizeButton.Text = "-"
                wait(0.3)
                spawn(
                    function()
                        local maxY = 0
                        for _, child in pairs(GUI.ContentArea:GetChildren()) do
                            if child:IsA("GuiObject") then
                                local childBottom = child.Position.Y.Offset + child.Size.Y.Offset
                                if childBottom > maxY then
                                    maxY = childBottom
                                end
                            end
                        end
                        GUI.ContentArea.CanvasSize = UDim2.new(0, 0, 0, maxY + 20)
                    end
                )
            end
        end
    )

    local draggableFolder = Workspace:FindFirstChild("DraggableFolder")
    if draggableFolder then
        draggableFolder.ChildAdded:Connect(onItemAdded)
        draggableFolder.ChildRemoved:Connect(onItemRemoved)
    else
        warn("DraggableFolder not found! Item ESP may not work properly.")
    end

    local enemiesFolder = Workspace:FindFirstChild("Enemies")
    if enemiesFolder then
        enemiesFolder.ChildAdded:Connect(onEnemyAdded)
        enemiesFolder.ChildRemoved:Connect(onEnemyRemoved)
    else
        warn("Enemies folder not found! Enemy ESP may not work properly.")
    end

    -- MapArea event handlers
    local mapArea = Workspace:FindFirstChild("MapArea")
    if mapArea then
        local generatedGrids = mapArea:FindFirstChild("GeneratedGrids")
        if generatedGrids then
            local activeGrids = generatedGrids:FindFirstChild("ActiveGrids")
            if activeGrids then
                activeGrids.ChildAdded:Connect(onMapAreaAdded)
                activeGrids.ChildRemoved:Connect(onMapAreaRemoved)
            else
                warn("ActiveGrids not found! Map Area ESP may not work properly.")
            end
        else
            warn("GeneratedGrids not found! Map Area ESP may not work properly.")
        end
    else
        warn("MapArea not found! Map Area ESP may not work properly.")
    end

    LocalPlayer.CharacterAdded:Connect(onCharacterAdded)

    -- Add keyboard input handling for Right Alt key
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.RightAlt then
            toggleGUIVisibility()
        end
    end)

    -- Add middle mouse click handling for teleport to base
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.UserInputType == Enum.UserInputType.MouseButton3 then
            local success = teleportToLocation("Base")
            if success then
                print("✓ Teleported to Base!")
            else
                warn("✗ Failed to teleport to Base!")
            end
        end
    end)

    local espUpdateConnection =
        RunService.Heartbeat:Connect(
        function()
            updateESPVisibility()
            updateStatus()
        end
    )

    scanItems()
    scanEnemies()
    scanMapAreas()
    updateStatus()

    -- Initialize the lists
    spawn(function()
        wait(1)
        refreshItemList()
        refreshEnemyList()
        refreshMapAreaList()
    end)

    return GUI
end

StartDeadShelterScript()