-- Dead Shelter Script
-- Features: <PERSON><PERSON>, Enemy ESP, Teleportation
-- Author: Auto-Generated Script

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer
local Camera = Workspace.CurrentCamera
local PlayerGui = LocalPlayer:WaitForChild("PlayerGui")

-- Theme Colors
local THEME_RED = Color3.fromRGB(229, 9, 20)
local THEME_BLACK = Color3.fromRGB(20, 20, 20)
local THEME_DARK_GRAY = Color3.fromRGB(35, 35, 35)
local THEME_LIGHT_GRAY = Color3.fromRGB(70, 70, 70)
local THEME_WHITE = Color3.fromRGB(255, 255, 255)
local THEME_GRAY_BLUE = Color3.fromRGB(30, 35, 45)
local THEME_GREEN = Color3.fromRGB(0, 255, 0)
local THEME_YELLOW = Color3.fromRGB(255, 255, 0)
local THEME_ORANGE = Color3.fromRGB(255, 165, 0)

-- ESP Configuration
local ESP_CONFIG = {
    Items = {
        TextColor = THEME_GREEN,
        TextSize = 14,
        Font = Enum.Font.SourceSansBold,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0),
        Enabled = false,
        MaxDistance = 5000
    },
    Enemies = {
        TextColor = THEME_RED,
        TextSize = 16,
        Font = Enum.Font.SourceSansBold,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0),
        Enabled = false,
        MaxDistance = 10000
    }
}

-- Settings
local Settings = {
    ItemESP = false,
    EnemyESP = false,
    AutoCollectItems = false,
    ShowDistance = true,
    Transparency = 0.2,
    DragSpeed = 0.05,
    MinimizedState = false,
    ActiveTab = "Home",
    UISize = UDim2.new(0, 400, 0, 320),
    Invisible = false,
    GodMode = false
}

-- Storage for ESP objects
local espObjects = {
    items = {},
    enemies = {}
}

-- Teleport locations
local TELEPORT_LOCATIONS = {
    Base = {
        name = "Base",
        target = "Baseplate",
        workspace_path = "workspace.Baseplate"
    },
    Pawnshop = {
        name = "Pawnshop", 
        target = "Noob's Pawnshop",
        workspace_path = "workspace[\"Noob's Pawnshop\"]"
    }
}

-- Utility Functions
local function CreateElement(className, properties, children)
    local element = Instance.new(className)
    
    for property, value in pairs(properties) do
        element[property] = value
    end
    
    if children then
        for _, child in pairs(children) do
            child.Parent = element
        end
    end
    
    return element
end

local function getDistance(object)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        return math.huge
    end
    
    local playerPosition = LocalPlayer.Character.HumanoidRootPart.Position
    local objectPosition
    
    if object:IsA("Model") and object.PrimaryPart then
        objectPosition = object.PrimaryPart.Position
    elseif object:IsA("Part") then
        objectPosition = object.Position
    elseif object:IsA("Model") then
        -- Try to find a part in the model
        local part = object:FindFirstChildOfClass("Part")
        if part then
            objectPosition = part.Position
        else
            return math.huge
        end
    else
        return math.huge
    end
    
    return (playerPosition - objectPosition).Magnitude
end

-- ESP Functions
local function createItemESP(item)
    local billboardGui = CreateElement("BillboardGui", {
        Name = "ItemESP",
        Adornee = item:IsA("Model") and (item.PrimaryPart or item:FindFirstChildOfClass("Part")) or item,
        Size = UDim2.new(0, 200, 0, 50),
        StudsOffset = Vector3.new(0, 3, 0),
        AlwaysOnTop = true,
        Parent = item
    })
    
    local textLabel = CreateElement("TextLabel", {
        Name = "ESPText",
        Size = UDim2.new(1, 0, 1, 0),
        BackgroundTransparency = 1,
        Text = item.Name,
        TextColor3 = ESP_CONFIG.Items.TextColor,
        TextSize = ESP_CONFIG.Items.TextSize,
        Font = ESP_CONFIG.Items.Font,
        TextStrokeTransparency = ESP_CONFIG.Items.Outline and 0 or 1,
        TextStrokeColor3 = ESP_CONFIG.Items.OutlineColor,
        TextScaled = true,
        Parent = billboardGui
    })
    
    return billboardGui
end

local function createEnemyESP(enemy)
    local billboardGui = CreateElement("BillboardGui", {
        Name = "EnemyESP",
        Adornee = enemy:IsA("Model") and (enemy.PrimaryPart or enemy:FindFirstChildOfClass("Part")) or enemy,
        Size = UDim2.new(0, 200, 0, 60),
        StudsOffset = Vector3.new(0, 4, 0),
        AlwaysOnTop = true,
        Parent = enemy
    })
    
    local textLabel = CreateElement("TextLabel", {
        Name = "ESPText",
        Size = UDim2.new(1, 0, 1, 0),
        BackgroundTransparency = 1,
        Text = "ENEMY\n" .. enemy.Name,
        TextColor3 = ESP_CONFIG.Enemies.TextColor,
        TextSize = ESP_CONFIG.Enemies.TextSize,
        Font = ESP_CONFIG.Enemies.Font,
        TextStrokeTransparency = ESP_CONFIG.Enemies.Outline and 0 or 1,
        TextStrokeColor3 = ESP_CONFIG.Enemies.OutlineColor,
        TextScaled = true,
        Parent = billboardGui
    })
    
    return billboardGui
end

-- Update ESP visibility based on distance and settings
local function updateESPVisibility()
    -- Update item ESP
    for item, espGui in pairs(espObjects.items) do
        if item.Parent and espGui.Parent then
            local distance = getDistance(item)
            if distance <= ESP_CONFIG.Items.MaxDistance and Settings.ItemESP then
                espGui.Enabled = true
                if Settings.ShowDistance then
                    local textLabel = espGui:FindFirstChild("ESPText")
                    if textLabel then
                        textLabel.Text = string.format("%s\nDistance: %.1fm", item.Name, distance)
                    end
                end
            else
                espGui.Enabled = false
            end
        else
            -- Remove ESP if item no longer exists
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.items[item] = nil
        end
    end
    
    -- Update enemy ESP
    for enemy, espGui in pairs(espObjects.enemies) do
        if enemy.Parent and espGui.Parent then
            local distance = getDistance(enemy)
            if distance <= ESP_CONFIG.Enemies.MaxDistance and Settings.EnemyESP then
                espGui.Enabled = true
                if Settings.ShowDistance then
                    local textLabel = espGui:FindFirstChild("ESPText")
                    if textLabel then
                        textLabel.Text = string.format("ENEMY\n%s\nDistance: %.1fm", enemy.Name, distance)
                    end
                end
            else
                espGui.Enabled = false
            end
        else
            -- Remove ESP if enemy no longer exists
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.enemies[enemy] = nil
        end
    end
end

-- Scan for items in DraggableFolder
local function scanItems()
    local draggableFolder = Workspace:FindFirstChild("DraggableFolder")
    if not draggableFolder then
        warn("DraggableFolder not found in Workspace!")
        return
    end
    
    local foundItems = 0
    for _, item in pairs(draggableFolder:GetChildren()) do
        if not espObjects.items[item] then
            local espGui = createItemESP(item)
            espObjects.items[item] = espGui
            foundItems = foundItems + 1
            print(string.format("Item ESP created for: %s", item.Name))
        end
    end
    
    if foundItems > 0 then
        print(string.format("Found %d new items!", foundItems))
    end
end

-- Scan for enemies
local function scanEnemies()
    local enemiesFolder = Workspace:FindFirstChild("Enemies")
    if not enemiesFolder then
        warn("Enemies folder not found in Workspace!")
        return
    end
    
    local foundEnemies = 0
    for _, enemy in pairs(enemiesFolder:GetChildren()) do
        if not espObjects.enemies[enemy] then
            local espGui = createEnemyESP(enemy)
            espObjects.enemies[enemy] = espGui
            foundEnemies = foundEnemies + 1
            print(string.format("Enemy ESP created for: %s", enemy.Name))
        end
    end
    
    if foundEnemies > 0 then
        print(string.format("Found %d new enemies!", foundEnemies))
    end
end

-- Teleportation Functions
local function teleportToLocation(locationName)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        warn("Player character or HumanoidRootPart not found!")
        return false
    end
    
    local location = TELEPORT_LOCATIONS[locationName]
    if not location then
        warn("Unknown location: " .. tostring(locationName))
        return false
    end
    
    local target = Workspace:FindFirstChild(location.target)
    if not target then
        warn(string.format("%s (%s) not found in Workspace!", location.name, location.target))
        return false
    end
    
    local targetPosition
    if target:IsA("Model") and target.PrimaryPart then
        targetPosition = target.PrimaryPart.Position
    elseif target:IsA("Part") then
        targetPosition = target.Position
    elseif target:IsA("Model") then
        local part = target:FindFirstChildOfClass("Part")
        if part then
            targetPosition = part.Position
        else
            warn("No valid part found in " .. location.name)
            return false
        end
    else
        warn("Invalid target type for " .. location.name)
        return false
    end
    
    -- Teleport with offset
    local offsetPosition = targetPosition + Vector3.new(0, 5, 0)
    LocalPlayer.Character.HumanoidRootPart.CFrame = CFrame.new(offsetPosition)
    print(string.format("Teleported to %s!", location.name))
    return true
end

-- Clear all ESP
local function clearAllESP()
    -- Clear item ESP
    for item, espGui in pairs(espObjects.items) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.items = {}

    -- Clear enemy ESP
    for enemy, espGui in pairs(espObjects.enemies) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.enemies = {}

    print("All ESP cleared!")
end

-- Toggle functions
local function toggleItemESP(button)
    Settings.ItemESP = not Settings.ItemESP
    ESP_CONFIG.Items.Enabled = Settings.ItemESP

    -- Auto scan when enabled
    if Settings.ItemESP then
        scanItems()
    end

    updateESPVisibility()

    -- Update button appearance
    if button then
        button.Text = "📦 Item ESP: " .. (Settings.ItemESP and "ON" or "OFF")
        button.BackgroundColor3 = Settings.ItemESP and THEME_GREEN or THEME_GRAY_BLUE
    end

    print("Item ESP: " .. (Settings.ItemESP and "ON" or "OFF"))
end

local function toggleEnemyESP(button)
    Settings.EnemyESP = not Settings.EnemyESP
    ESP_CONFIG.Enemies.Enabled = Settings.EnemyESP

    -- Auto scan when enabled
    if Settings.EnemyESP then
        scanEnemies()
    end

    updateESPVisibility()

    -- Update button appearance
    if button then
        button.Text = "👹 Enemy ESP: " .. (Settings.EnemyESP and "ON" or "OFF")
        button.BackgroundColor3 = Settings.EnemyESP and THEME_RED or THEME_GRAY_BLUE
    end

    print("Enemy ESP: " .. (Settings.EnemyESP and "ON" or "OFF"))
end

-- Function to handle character respawn
local function onCharacterAdded(character)
    wait(1) -- Wait for character to fully load

    -- Reapply invisibility if it was enabled
    if Settings.Invisible then
        Settings.Invisible = false -- Reset to trigger the toggle
        toggleInvisible()
    end

    -- Reapply god mode if it was enabled
    if Settings.GodMode then
        Settings.GodMode = false -- Reset to trigger the toggle
        toggleGodMode()
    end
end

-- Invisible Functions
local originalTransparency = {}
local originalCanCollide = {}

local function makeInvisible()
    if not LocalPlayer.Character then
        warn("Character not found!")
        return false
    end

    Settings.Invisible = true

    for _, part in pairs(LocalPlayer.Character:GetChildren()) do
        if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
            originalTransparency[part] = part.Transparency
            originalCanCollide[part] = part.CanCollide
            part.Transparency = 1
            part.CanCollide = false
        elseif part:IsA("Accessory") then
            local handle = part:FindFirstChild("Handle")
            if handle then
                originalTransparency[handle] = handle.Transparency
                originalCanCollide[handle] = handle.CanCollide
                handle.Transparency = 1
                handle.CanCollide = false
            end
        end
    end

    -- Hide face if exists
    local head = LocalPlayer.Character:FindFirstChild("Head")
    if head then
        local face = head:FindFirstChild("face")
        if face then
            originalTransparency[face] = face.Transparency
            face.Transparency = 1
        end
    end

    print("Character is now invisible!")
    return true
end

local function makeVisible()
    if not LocalPlayer.Character then
        warn("Character not found!")
        return false
    end

    Settings.Invisible = false

    for part, transparency in pairs(originalTransparency) do
        if part and part.Parent then
            part.Transparency = transparency
        end
    end

    for part, canCollide in pairs(originalCanCollide) do
        if part and part.Parent and part.Name ~= "HumanoidRootPart" then
            part.CanCollide = canCollide
        end
    end

    -- Clear stored values
    originalTransparency = {}
    originalCanCollide = {}

    print("Character is now visible!")
    return true
end

local function toggleInvisible()
    if Settings.Invisible then
        makeVisible()
    else
        makeInvisible()
    end
end

-- God Mode Functions
local godModeConnection = nil

local function enableGodMode()
    if not LocalPlayer.Character then
        warn("Character not found!")
        return false
    end

    local humanoid = LocalPlayer.Character:FindFirstChild("Humanoid")
    if not humanoid then
        warn("Humanoid not found!")
        return false
    end

    Settings.GodMode = true

    -- Method 1: Constantly reset health
    godModeConnection = RunService.Heartbeat:Connect(function()
        if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("Humanoid") then
            local hum = LocalPlayer.Character.Humanoid
            if hum.Health < hum.MaxHealth then
                hum.Health = hum.MaxHealth
            end
        end
    end)

    -- Method 2: Remove damage scripts (if any)
    for _, script in pairs(LocalPlayer.Character:GetDescendants()) do
        if script:IsA("Script") and (script.Name:lower():find("damage") or script.Name:lower():find("hurt")) then
            script:Destroy()
        end
    end

    print("God Mode enabled!")
    return true
end

local function disableGodMode()
    Settings.GodMode = false

    if godModeConnection then
        godModeConnection:Disconnect()
        godModeConnection = nil
    end

    print("God Mode disabled!")
    return true
end

local function toggleGodMode()
    if Settings.GodMode then
        disableGodMode()
    else
        enableGodMode()
    end
end

-- Event handlers for new items/enemies
local function onItemAdded(item)
    wait(0.1) -- Wait for item to fully load
    if not espObjects.items[item] then
        local espGui = createItemESP(item)
        espObjects.items[item] = espGui
        print(string.format("New item ESP created: %s", item.Name))
    end
end

local function onEnemyAdded(enemy)
    wait(0.1) -- Wait for enemy to fully load
    if not espObjects.enemies[enemy] then
        local espGui = createEnemyESP(enemy)
        espObjects.enemies[enemy] = espGui
        print(string.format("New enemy ESP created: %s", enemy.Name))
    end
end

local function onItemRemoved(item)
    if espObjects.items[item] then
        if espObjects.items[item].Parent then
            espObjects.items[item]:Destroy()
        end
        espObjects.items[item] = nil
        print(string.format("Item ESP removed: %s", item.Name))
    end
end

local function onEnemyRemoved(enemy)
    if espObjects.enemies[enemy] then
        if espObjects.enemies[enemy].Parent then
            espObjects.enemies[enemy]:Destroy()
        end
        espObjects.enemies[enemy] = nil
        print(string.format("Enemy ESP removed: %s", enemy.Name))
    end
end

-- GUI Creation
local function CreateGUI()
    local ScreenGui = CreateElement("ScreenGui", {
        Name = "DeadShelterGUI",
        ResetOnSpawn = false,
        ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
        Parent = PlayerGui
    })

    local MainFrame = CreateElement("Frame", {
        Name = "MainFrame",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME_BLACK,
        BackgroundTransparency = Settings.Transparency,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 0, 0.5, 0),
        Size = Settings.UISize,
        Parent = ScreenGui
    })

    local UICorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = MainFrame
    })

    local TitleBar = CreateElement("Frame", {
        Name = "TitleBar",
        BackgroundColor3 = THEME_RED,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 40),
        Parent = MainFrame
    })

    local TitleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = TitleBar
    })

    local BottomFrame = CreateElement("Frame", {
        BackgroundColor3 = THEME_RED,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0.5, 0),
        Size = UDim2.new(1, 0, 0.5, 0),
        Parent = TitleBar
    })

    local TitleText = CreateElement("TextLabel", {
        Name = "TitleText",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 0),
        Size = UDim2.new(1, -120, 1, 0),
        Font = Enum.Font.GothamBold,
        Text = "Dead Shelter Script",
        TextColor3 = THEME_WHITE,
        TextSize = 18,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = TitleBar
    })

    local ControlsFrame = CreateElement("Frame", {
        Name = "ControlsFrame",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -10, 0.5, 0),
        Size = UDim2.new(0, 90, 0, 24),
        Parent = TitleBar
    })

    local MinimizeButton = CreateElement("TextButton", {
        Name = "MinimizeButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -50, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "-",
        TextColor3 = THEME_WHITE,
        TextSize = 24,
        Parent = ControlsFrame
    })

    local CloseButton = CreateElement("TextButton", {
        Name = "CloseButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, 0, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "×",
        TextColor3 = THEME_WHITE,
        TextSize = 24,
        Parent = ControlsFrame
    })

    -- Content Area
    local ContentArea = CreateElement("Frame", {
        Name = "ContentArea",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 50),
        Size = UDim2.new(1, -30, 1, -65),
        ClipsDescendants = true,
        Parent = MainFrame
    })

    -- Create buttons and controls
    local function createButton(text, position, size, color, callback)
        local button = CreateElement("TextButton", {
            BackgroundColor3 = color or THEME_GRAY_BLUE,
            BackgroundTransparency = 0.3,
            BorderSizePixel = 0,
            Position = position,
            Size = size or UDim2.new(1, 0, 0, 35),
            Font = Enum.Font.GothamSemibold,
            Text = text,
            TextColor3 = THEME_WHITE,
            TextSize = 14,
            Parent = ContentArea
        })

        local buttonCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = button
        })

        if callback then
            button.MouseButton1Click:Connect(callback)
        end

        return button
    end

    -- ESP Section
    local espLabel = CreateElement("TextLabel", {
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(1, 0, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = "ESP Controls",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = ContentArea
    })

    -- Item ESP Toggle
    local itemESPButton = createButton("📦 Item ESP: OFF", UDim2.new(0, 0, 0, 30), UDim2.new(0.48, 0, 0, 35), THEME_GRAY_BLUE, function()
        toggleItemESP(itemESPButton)
    end)

    -- Enemy ESP Toggle
    local enemyESPButton = createButton("👹 Enemy ESP: OFF", UDim2.new(0.52, 0, 0, 30), UDim2.new(0.48, 0, 0, 35), THEME_GRAY_BLUE, function()
        toggleEnemyESP(enemyESPButton)
    end)

    -- Teleport Section
    local teleportLabel = CreateElement("TextLabel", {
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 80),
        Size = UDim2.new(1, 0, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = "Teleport Controls",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = ContentArea
    })

    -- Teleport Buttons
    local teleportBaseButton = createButton("🏠 Teleport to Base", UDim2.new(0, 0, 0, 110), UDim2.new(0.48, 0, 0, 35), THEME_GREEN, function()
        teleportToLocation("Base")
    end)

    local teleportPawnshopButton = createButton("🏪 Teleport to Pawnshop", UDim2.new(0.52, 0, 0, 110), UDim2.new(0.48, 0, 0, 35), THEME_YELLOW, function()
        teleportToLocation("Pawnshop")
    end)

    -- Character Section
    local characterLabel = CreateElement("TextLabel", {
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 160),
        Size = UDim2.new(1, 0, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = "Character Controls",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = ContentArea
    })

    -- Invisibility Toggle
    local invisibilityButton = createButton("👻 Invisibility: OFF", UDim2.new(0, 0, 0, 190), UDim2.new(0.48, 0, 0, 35), THEME_GRAY_BLUE, function()
        toggleInvisible()
        invisibilityButton.Text = "👻 Invisibility: " .. (Settings.Invisible and "ON" or "OFF")
        invisibilityButton.BackgroundColor3 = Settings.Invisible and THEME_YELLOW or THEME_GRAY_BLUE
    end)

    -- God Mode Toggle
    local godModeButton = createButton("🛡️ God Mode: OFF", UDim2.new(0.52, 0, 0, 190), UDim2.new(0.48, 0, 0, 35), THEME_GRAY_BLUE, function()
        toggleGodMode()
        godModeButton.Text = "🛡️ God Mode: " .. (Settings.GodMode and "ON" or "OFF")
        godModeButton.BackgroundColor3 = Settings.GodMode and THEME_GREEN or THEME_GRAY_BLUE
    end)

    -- Status Label
    local statusLabel = CreateElement("TextLabel", {
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 240),
        Size = UDim2.new(1, 0, 0, 60),
        Font = Enum.Font.Gotham,
        Text = "Status: Ready\nItems: 0 | Enemies: 0\nInvisible: OFF | God: OFF",
        TextColor3 = THEME_WHITE,
        TextSize = 12,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = ContentArea
    })

    return {
        ScreenGui = ScreenGui,
        MainFrame = MainFrame,
        TitleBar = TitleBar,
        MinimizeButton = MinimizeButton,
        CloseButton = CloseButton,
        ContentArea = ContentArea,
        StatusLabel = statusLabel,
        ItemESPButton = itemESPButton,
        EnemyESPButton = enemyESPButton,
        InvisibilityButton = invisibilityButton,
        GodModeButton = godModeButton
    }
end

-- Update status function
local function updateStatus(gui)
    if not gui or not gui.StatusLabel then return end

    local itemCount = 0
    local enemyCount = 0

    for _ in pairs(espObjects.items) do
        itemCount = itemCount + 1
    end

    for _ in pairs(espObjects.enemies) do
        enemyCount = enemyCount + 1
    end

    gui.StatusLabel.Text = string.format("Status: Active\nItems: %d | Enemies: %d\nInvisible: %s | God: %s",
        itemCount, enemyCount,
        Settings.Invisible and "ON" or "OFF",
        Settings.GodMode and "ON" or "OFF")
end

-- Main initialization function
local function StartDeadShelterScript()
    -- Create GUI
    local GUI = CreateGUI()

    -- Setup dragging functionality
    local Dragging = false
    local DragInput
    local DragStart
    local StartPos

    local function UpdateDrag()
        if not DragInput then return end

        local Delta = DragInput.Position - DragStart
        local Position = UDim2.new(StartPos.X.Scale, StartPos.X.Offset + Delta.X, StartPos.Y.Scale, StartPos.Y.Offset + Delta.Y)

        TweenService:Create(GUI.MainFrame, TweenInfo.new(Settings.DragSpeed), {Position = Position}):Play()
    end

    GUI.TitleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Dragging = true
            DragStart = input.Position
            StartPos = GUI.MainFrame.Position

            input.Changed:Connect(function()
                if input.UserInputState == Enum.UserInputState.End then
                    Dragging = false
                    DragInput = nil
                end
            end)
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseMovement and Dragging then
            DragInput = input
            UpdateDrag()
        end
    end)

    -- Close button functionality
    GUI.CloseButton.MouseButton1Click:Connect(function()
        GUI.ScreenGui:Destroy()
        clearAllESP()
        print("Dead Shelter Script closed!")
    end)

    -- Minimize button functionality
    GUI.MinimizeButton.MouseButton1Click:Connect(function()
        Settings.MinimizedState = not Settings.MinimizedState

        if Settings.MinimizedState then
            -- Hide content area when minimized
            GUI.ContentArea.Visible = false
            TweenService:Create(GUI.MainFrame, TweenInfo.new(0.3), {
                Size = UDim2.new(0, Settings.UISize.X.Offset, 0, 40)
            }):Play()
            GUI.MinimizeButton.Text = "+"
        else
            -- Show content area when restored
            GUI.ContentArea.Visible = true
            TweenService:Create(GUI.MainFrame, TweenInfo.new(0.3), {
                Size = Settings.UISize
            }):Play()
            GUI.MinimizeButton.Text = "-"
        end
    end)

    -- Setup event listeners for new items/enemies
    local draggableFolder = Workspace:FindFirstChild("DraggableFolder")
    if draggableFolder then
        draggableFolder.ChildAdded:Connect(onItemAdded)
        draggableFolder.ChildRemoved:Connect(onItemRemoved)
        print("DraggableFolder event listeners setup!")
    else
        warn("DraggableFolder not found! Item ESP may not work properly.")
    end

    local enemiesFolder = Workspace:FindFirstChild("Enemies")
    if enemiesFolder then
        enemiesFolder.ChildAdded:Connect(onEnemyAdded)
        enemiesFolder.ChildRemoved:Connect(onEnemyRemoved)
        print("Enemies folder event listeners setup!")
    else
        warn("Enemies folder not found! Enemy ESP may not work properly.")
    end

    -- Setup character respawn event listener
    LocalPlayer.CharacterAdded:Connect(onCharacterAdded)
    print("Character respawn event listener setup!")

    -- Start ESP update loop
    local espUpdateConnection = RunService.Heartbeat:Connect(function()
        updateESPVisibility()
        updateStatus(GUI)
    end)

    -- Initial scan
    print("Dead Shelter Script initialized!")
    print("Performing initial scan...")
    scanItems()
    scanEnemies()
    updateStatus(GUI)

    print("=== Dead Shelter Script Ready ===")
    print("Features:")
    print("- Item ESP (DraggableFolder)")
    print("- Enemy ESP (Enemies folder)")
    print("- Teleport to Base (Baseplate)")
    print("- Teleport to Pawnshop (Noob's Pawnshop)")
    print("- Character Invisibility Toggle")
    print("- God Mode (Infinite Health)")
    print("Use the GUI to control all features!")

    return GUI
end

-- Auto-start the script
StartDeadShelterScript()
