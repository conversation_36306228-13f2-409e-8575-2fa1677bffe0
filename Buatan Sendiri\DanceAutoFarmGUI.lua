local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local ContextActionService = game:GetService("ContextActionService")
local CoreGui = game:GetService("CoreGui")

local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")
local DanceEvent = ReplicatedStorage:WaitForChild("RemoteEvents"):WaitForChild("DanceEvent")
local DanceConfig = require(ReplicatedStorage:WaitForChild("Configs"):WaitFor<PERSON>hild("DanceConfig"))

local NETFLIX_RED = Color3.fromRGB(229, 9, 20)
local NETFLIX_BLACK = Color3.fromRGB(20, 20, 20)
local NETFLIX_DARK_GRAY = Color3.fromRGB(35, 35, 35)
local NETFLIX_LIGHT_GRAY = Color3.fromRGB(70, 70, 70)
local NETFLIX_WHITE = Color3.fromRGB(255, 255, 255)
local NETFLIX_GRAY_BLUE = Color3.fromRGB(30, 35, 45)

local Settings = {
    AutoFarm = false,
    DanceInterval = 26,
    RandomDance = true,
    SelectedDance = 10,
    Transparency = 0.2,
    DragSpeed = 0.05,
    MinimizedState = false,
    ActiveTab = "Home",
    UISize = UDim2.new(0, 500, 0, 300)
}

local function CreateElement(className, properties, children)
    local element = Instance.new(className)

    for property, value in pairs(properties) do
        element[property] = value
    end

    if children then
        for _, child in pairs(children) do
            child.Parent = element
        end
    end

    return element
end

local function CreateGUI()
    local ScreenGui = CreateElement("ScreenGui", {
        Name = "DanceAutoFarmGUI_V2",
        ResetOnSpawn = false,
        ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
        Parent = PlayerGui
    })

    local MainFrame = CreateElement("Frame", {
        Name = "MainFrame",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = NETFLIX_BLACK,
        BackgroundTransparency = Settings.Transparency,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 0, 0.5, 0),
        Size = Settings.UISize,
        Parent = ScreenGui
    })

    local UICorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = MainFrame
    })

    local TitleBar = CreateElement("Frame", {
        Name = "TitleBar",
        BackgroundColor3 = NETFLIX_RED,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 40),
        Parent = MainFrame
    })

    local TitleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = TitleBar
    })

    local BottomFrame = CreateElement("Frame", {
        BackgroundColor3 = NETFLIX_RED,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0.5, 0),
        Size = UDim2.new(1, 0, 0.5, 0),
        Parent = TitleBar
    })

    local TitleText = CreateElement("TextLabel", {
        Name = "TitleText",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 0),
        Size = UDim2.new(1, -120, 1, 0),
        Font = Enum.Font.GothamBold,
        Text = "Dance Auto Farm V2",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 18,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = TitleBar
    })

    local ControlsFrame = CreateElement("Frame", {
        Name = "ControlsFrame",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -10, 0.5, 0),
        Size = UDim2.new(0, 90, 0, 24),
        Parent = TitleBar
    })

    local MinimizeButton = CreateElement("TextButton", {
        Name = "MinimizeButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -50, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "-",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 24,
        Parent = ControlsFrame
    })

    local ResizeButton = CreateElement("TextButton", {
        Name = "ResizeButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -25, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "□",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 18,
        Parent = ControlsFrame
    })

    local CloseButton = CreateElement("TextButton", {
        Name = "CloseButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, 0, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "×",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 24,
        Parent = ControlsFrame
    })

    local Dragging = false
    local DragInput
    local DragStart
    local StartPos

    local function UpdateDrag()
        if not DragInput then return end

        local Delta = DragInput.Position - DragStart
        local Position = UDim2.new(StartPos.X.Scale, StartPos.X.Offset + Delta.X, StartPos.Y.Scale, StartPos.Y.Offset + Delta.Y)

        TweenService:Create(MainFrame, TweenInfo.new(Settings.DragSpeed), {Position = Position}):Play()
    end

    TitleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Dragging = true
            DragStart = input.Position
            StartPos = MainFrame.Position

            input.Changed:Connect(function()
                if input.UserInputState == Enum.UserInputState.End then
                    Dragging = false
                    DragInput = nil
                end
            end)
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseMovement and Dragging then
            DragInput = input
            UpdateDrag()
        end
    end)

    CloseButton.MouseButton1Click:Connect(function()
        ScreenGui:Destroy()
        Settings.AutoFarm = false
    end)

    MinimizeButton.MouseButton1Click:Connect(function()
        Settings.MinimizedState = not Settings.MinimizedState

        if Settings.MinimizedState then
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = UDim2.new(0, Settings.UISize.X.Offset, 0, 40)
            }):Play()
        else
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = Settings.UISize
            }):Play()
        end
    end)

    local IsMaximized = false
    local OriginalSize = Settings.UISize
    local OriginalPosition = UDim2.new(0.5, 0, 0.5, 0)

    ResizeButton.MouseButton1Click:Connect(function()
        IsMaximized = not IsMaximized

        if IsMaximized then
            OriginalSize = MainFrame.Size
            OriginalPosition = MainFrame.Position

            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = UDim2.new(0.8, 0, 0.8, 0),
                Position = UDim2.new(0.5, 0, 0.5, 0)
            }):Play()
        else
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = OriginalSize,
                Position = OriginalPosition
            }):Play()
        end
    end)

    local MainLayout = CreateElement("Frame", {
        Name = "MainLayout",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 40),
        Size = UDim2.new(1, 0, 1, -40),
        ClipsDescendants = true,
        Parent = MainFrame
    })

    local Sidebar = CreateElement("Frame", {
        Name = "Sidebar",
        BackgroundColor3 = NETFLIX_DARK_GRAY,
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(0, 120, 1, 0),
        Parent = MainLayout
    })

    local SidebarCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = Sidebar
    })

    local SidebarFix = CreateElement("Frame", {
        BackgroundColor3 = NETFLIX_DARK_GRAY,
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 0, 0, 0),
        Size = UDim2.new(0.5, 0, 1, 0),
        Parent = Sidebar
    })

    local MenuContainer = CreateElement("Frame", {
        Name = "MenuContainer",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 10),
        Size = UDim2.new(1, 0, 1, -20),
        Parent = Sidebar
    })

    local MenuList = CreateElement("UIListLayout", {
        Padding = UDim.new(0, 8),
        HorizontalAlignment = Enum.HorizontalAlignment.Center,
        SortOrder = Enum.SortOrder.LayoutOrder,
        Parent = MenuContainer
    })

    local ContentArea = CreateElement("Frame", {
        Name = "ContentArea",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 120, 0, 0),
        Size = UDim2.new(1, -120, 1, 0),
        Parent = MainLayout
    })

    local MenuItems = {
        {Name = "Home", Icon = "🏠", Order = 1},
        {Name = "Settings", Icon = "⚙️", Order = 2}
    }

    local MenuButtons = {}
    local ContentFrames = {}

    for _, item in ipairs(MenuItems) do
        local MenuButton = CreateElement("TextButton", {
            Name = item.Name .. "Button",
            BackgroundColor3 = Settings.ActiveTab == item.Name and NETFLIX_RED or NETFLIX_GRAY_BLUE,
            BackgroundTransparency = Settings.ActiveTab == item.Name and 0.1 or 0.5,
            BorderSizePixel = 0,
            Size = UDim2.new(0.9, 0, 0, 36),
            Font = Enum.Font.GothamSemibold,
            Text = item.Icon .. " " .. item.Name,
            TextColor3 = NETFLIX_WHITE,
            TextSize = 14,
            LayoutOrder = item.Order,
            Parent = MenuContainer
        })

        local ButtonCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = MenuButton
        })

        local ContentFrame = CreateElement("ScrollingFrame", {
            Name = item.Name .. "Content",
            BackgroundTransparency = 1,
            BorderSizePixel = 0,
            Position = UDim2.new(0, 15, 0, 15),
            Size = UDim2.new(1, -30, 1, -30),
            CanvasSize = UDim2.new(0, 0, 0, 0),
            ScrollBarThickness = 4,
            ScrollBarImageColor3 = NETFLIX_RED,
            Visible = Settings.ActiveTab == item.Name,
            Parent = ContentArea
        })

        MenuButtons[item.Name] = MenuButton
        ContentFrames[item.Name] = ContentFrame

        MenuButton.MouseButton1Click:Connect(function()
            Settings.ActiveTab = item.Name

            for name, button in pairs(MenuButtons) do
                TweenService:Create(button, TweenInfo.new(0.2), {
                    BackgroundColor3 = name == item.Name and NETFLIX_RED or NETFLIX_GRAY_BLUE,
                    BackgroundTransparency = name == item.Name and 0.1 or 0.5
                }):Play()
            end

            for name, frame in pairs(ContentFrames) do
                frame.Visible = name == item.Name
            end
        end)
    end

    local HomeContent = ContentFrames["Home"]

    local StatusCard = CreateElement("Frame", {
        Name = "StatusCard",
        BackgroundColor3 = NETFLIX_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local StatusCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = StatusCard
    })

    local StatusTitle = CreateElement("TextLabel", {
        Name = "StatusTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Farm Status",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = StatusCard
    })

    local StatusLabel = CreateElement("TextLabel", {
        Name = "StatusLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = StatusCard
    })

    local ToggleButton = CreateElement("Frame", {
        Name = "ToggleButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = NETFLIX_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = StatusCard
    })

    local ToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = ToggleButton
    })

    local ToggleCircle = CreateElement("Frame", {
        Name = "ToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = NETFLIX_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = ToggleButton
    })

    local CircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = ToggleCircle
    })

    local StatsCard = CreateElement("Frame", {
        Name = "StatsCard",
        BackgroundColor3 = NETFLIX_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 90),
        Size = UDim2.new(1, 0, 0, 60),
        Parent = HomeContent
    })

    local StatsCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = StatsCard
    })

    local DanceCountLabel = CreateElement("TextLabel", {
        Name = "DanceCountLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Dances Performed: 0",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = StatsCard
    })

    local TimeRunningLabel = CreateElement("TextLabel", {
        Name = "TimeRunningLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 30),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Time Running: 00:00:00",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = StatsCard
    })

    local SettingsContent = ContentFrames["Settings"]

    local IntervalCard = CreateElement("Frame", {
        Name = "IntervalCard",
        BackgroundColor3 = NETFLIX_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 80),
        Parent = SettingsContent
    })

    local IntervalCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = IntervalCard
    })

    local IntervalTitle = CreateElement("TextLabel", {
        Name = "IntervalTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Dance Interval",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = IntervalCard
    })

    local IntervalLabel = CreateElement("TextLabel", {
        Name = "IntervalLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 35),
        Size = UDim2.new(0, 100, 0, 20),
        Font = Enum.Font.Gotham,
        Text = Settings.DanceInterval .. " seconds",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = IntervalCard
    })

    local IntervalSlider = CreateElement("Frame", {
        Name = "IntervalSlider",
        BackgroundColor3 = NETFLIX_LIGHT_GRAY,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 120, 0, 45),
        Size = UDim2.new(0.7, -130, 0, 6),
        Parent = IntervalCard
    })

    local SliderCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = IntervalSlider
    })

    local SliderFill = CreateElement("Frame", {
        Name = "SliderFill",
        BackgroundColor3 = NETFLIX_RED,
        BorderSizePixel = 0,
        Size = UDim2.new((Settings.DanceInterval - 5) / 25, 0, 1, 0),
        Parent = IntervalSlider
    })

    local FillCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = SliderFill
    })

    local SliderButton = CreateElement("TextButton", {
        Name = "SliderButton",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = NETFLIX_WHITE,
        Position = UDim2.new((Settings.DanceInterval - 5) / 25, 0, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Text = "",
        Parent = IntervalSlider
    })

    local ButtonCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = SliderButton
    })

    local RandomCard = CreateElement("Frame", {
        Name = "RandomCard",
        BackgroundColor3 = NETFLIX_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 90),
        Size = UDim2.new(1, 0, 0, 60),
        Parent = SettingsContent
    })

    local RandomCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = RandomCard
    })

    local RandomTitle = CreateElement("TextLabel", {
        Name = "RandomTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Random Dance",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RandomCard
    })

    local RandomLabel = CreateElement("TextLabel", {
        Name = "RandomLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 30),
        Size = UDim2.new(0.7, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Use random dances instead of a specific one",
        TextColor3 = NETFLIX_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RandomCard
    })

    local RandomToggle = CreateElement("Frame", {
        Name = "RandomToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = Settings.RandomDance and NETFLIX_RED or NETFLIX_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = RandomCard
    })

    local RandomCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = RandomToggle
    })

    local RandomCircle = CreateElement("Frame", {
        Name = "RandomCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = NETFLIX_WHITE,
        Position = Settings.RandomDance and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = RandomToggle
    })

    local RandomCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = RandomCircle
    })

    ToggleButton.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoFarm = not Settings.AutoFarm
            UpdateToggle()
            UpdateStatus()
        end
    end)

    RandomToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.RandomDance = not Settings.RandomDance
            UpdateRandomToggle()
        end
    end)

    local function UpdateSlider(input)
        local SliderPosition = math.clamp((input.Position.X - IntervalSlider.AbsolutePosition.X) / IntervalSlider.AbsoluteSize.X, 0, 1)
        Settings.DanceInterval = math.floor(SliderPosition * 25 + 5)

        SliderFill.Size = UDim2.new(SliderPosition, 0, 1, 0)
        SliderButton.Position = UDim2.new(SliderPosition, 0, 0.5, 0)
        IntervalLabel.Text = Settings.DanceInterval .. " seconds"
    end

    SliderButton.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    IntervalSlider.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            UpdateSlider(input)

            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    -- Update functions
    function UpdateToggle()
        local TogglePos = Settings.AutoFarm and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoFarm and NETFLIX_RED or NETFLIX_LIGHT_GRAY

        TweenService:Create(ToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(ToggleButton, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
    end

    function UpdateRandomToggle()
        local TogglePos = Settings.RandomDance and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.RandomDance and NETFLIX_RED or NETFLIX_LIGHT_GRAY

        TweenService:Create(RandomCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(RandomToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
    end

    function UpdateStatus()
        StatusLabel.Text = "Status: " .. (Settings.AutoFarm and "Running" or "Idle")
    end

    -- Initialize toggle states
    UpdateToggle()

    return {
        ScreenGui = ScreenGui,
        StatusLabel = StatusLabel
    }
end

-- Auto Farm Function
local function StartAutoFarm()
    local GUI = CreateGUI()
    local LastDanceTime = 0
    local StartTime = tick()
    local DanceCount = 0

    -- Main loop
    while wait(0.1) do
        if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
            break
        end

        if Settings.AutoFarm then
            local CurrentTime = tick()

            if CurrentTime - LastDanceTime >= Settings.DanceInterval then
                -- Update status
                GUI.StatusLabel.Text = "Status: Dancing..."

                -- Select dance ID
                local DanceId = Settings.SelectedDance
                if Settings.RandomDance then
                    DanceId = math.random(1, #DanceConfig)
                end

                -- Fire dance event
                DanceEvent:FireServer(DanceId)

                -- Update last dance time
                LastDanceTime = CurrentTime

                -- Update dance count
                DanceCount = DanceCount + 1

                -- Update status after dance
                wait(1)
                GUI.StatusLabel.Text = "Status: Running"
            end
        end
    end
end

-- Start the auto farm
StartAutoFarm()
