--[[
    Example Custom Module for Roblox Script Hub
    This file shows how to create and integrate a custom module
]]

-- Pastikan Script Hub sudah diload
if not _G.RobloxScriptHub then
    error("Roblox Script Hub must be loaded first!")
end

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Player = Players.LocalPlayer

-- Example: Auto Rebirth Module
local AutoRebirthModule = {
    Name = "AutoRebirth",
    DisplayName = "Auto Rebirth",
    Description = "Automatically rebirths when conditions are met",
    DefaultSettings = {
        Enabled = false,
        MinLevel = 100,
        Interval = 5,
        RemotePath = "ReplicatedStorage.Events.Rebirth"
    },
    
    Start = function(settings)
        local connection
        local remote = nil
        
        -- Try to find the rebirth remote
        local success, result = pcall(function()
            local parts = string.split(settings.RemotePath, ".")
            local current = game
            for _, part in ipairs(parts) do
                current = current:WaitForChild(part, 5)
            end
            return current
        end)
        
        if success then
            remote = result
            print("AutoRebirth: Found remote at " .. settings.RemotePath)
        else
            warn("AutoRebirth: Could not find remote at " .. settings.RemotePath)
            return nil
        end
        
        connection = RunService.Heartbeat:Connect(function()
            if settings.Enabled and remote then
                -- Example condition: check player level
                local character = Player.Character
                if character then
                    local humanoid = character:FindFirstChild("Humanoid")
                    if humanoid then
                        -- Assuming level is stored in leaderstats
                        local leaderstats = Player:FindFirstChild("leaderstats")
                        if leaderstats then
                            local level = leaderstats:FindFirstChild("Level")
                            if level and level.Value >= settings.MinLevel then
                                remote:FireServer()
                                print("AutoRebirth: Rebirthed at level " .. level.Value)
                                wait(settings.Interval)
                            end
                        end
                    end
                end
                wait(0.1) -- Prevent excessive checking
            end
        end)
        
        return {
            Connection = connection,
            Remote = remote
        }
    end,
    
    Stop = function(moduleData)
        if moduleData and moduleData.Connection then
            moduleData.Connection:Disconnect()
            print("AutoRebirth: Module stopped")
        end
    end
}

-- Example: Auto Collect Pets Module
local AutoCollectPetsModule = {
    Name = "AutoCollectPets",
    DisplayName = "Auto Collect Pets",
    Description = "Automatically collects pets in workspace",
    DefaultSettings = {
        Enabled = false,
        Interval = 0.5,
        PetNames = {"Pet", "Egg", "Chest"}
    },
    
    Start = function(settings)
        local connection
        
        connection = RunService.Heartbeat:Connect(function()
            if settings.Enabled then
                local character = Player.Character
                if character then
                    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
                    if humanoidRootPart then
                        -- Look for pets in workspace
                        for _, petName in ipairs(settings.PetNames) do
                            for _, pet in ipairs(workspace:GetChildren()) do
                                if pet.Name == petName and pet:FindFirstChild("TouchInterest") then
                                    -- Use firetouchinterest if available
                                    if firetouchinterest then
                                        firetouchinterest(humanoidRootPart, pet, 0)
                                        firetouchinterest(humanoidRootPart, pet, 1)
                                    end
                                end
                            end
                        end
                    end
                end
                wait(settings.Interval)
            end
        end)
        
        return {
            Connection = connection
        }
    end,
    
    Stop = function(moduleData)
        if moduleData and moduleData.Connection then
            moduleData.Connection:Disconnect()
            print("AutoCollectPets: Module stopped")
        end
    end
}

-- Example: Teleport Module
local TeleportModule = {
    Name = "Teleport",
    DisplayName = "Teleport Tools",
    Description = "Quick teleportation to common locations",
    DefaultSettings = {
        Enabled = false,
        Locations = {
            Spawn = Vector3.new(0, 10, 0),
            Shop = Vector3.new(100, 10, 100),
            Farm = Vector3.new(-100, 10, -100)
        }
    },
    
    Start = function(settings)
        -- This module doesn't need a continuous loop
        -- It provides teleport functions that can be called
        
        local teleportFunctions = {}
        
        for locationName, position in pairs(settings.Locations) do
            teleportFunctions[locationName] = function()
                local character = Player.Character
                if character then
                    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
                    if humanoidRootPart then
                        humanoidRootPart.CFrame = CFrame.new(position)
                        print("Teleported to " .. locationName)
                    end
                end
            end
        end
        
        return {
            TeleportFunctions = teleportFunctions
        }
    end,
    
    Stop = function(moduleData)
        print("Teleport: Module stopped")
    end
}

-- Register the custom modules
_G.RobloxScriptHub.RegisterModule(AutoRebirthModule)
_G.RobloxScriptHub.RegisterModule(AutoCollectPetsModule)
_G.RobloxScriptHub.RegisterModule(TeleportModule)

print("✅ Custom modules loaded successfully!")
print("📋 Added modules: AutoRebirth, AutoCollectPets, Teleport")

-- Example of how to create custom UI for these modules
-- You would need to modify the main script to add tabs for these modules
-- Or create them dynamically:

--[[
-- Example of adding a custom tab (this would go in the main script)
local function CreateCustomModuleContent(contentFrame, moduleName)
    if moduleName == "AutoRebirth" then
        local module = _G.RobloxScriptHub.ModuleSystem:GetModule(moduleName)
        local settings = _G.RobloxScriptHub.Settings.ModuleSettings[moduleName]
        
        -- Create UI elements specific to AutoRebirth
        -- Min Level input, Remote Path input, etc.
        
    elseif moduleName == "Teleport" then
        -- Create teleport buttons for each location
        local module = _G.RobloxScriptHub.ModuleSystem:GetModule(moduleName)
        local settings = _G.RobloxScriptHub.Settings.ModuleSettings[moduleName]
        
        for locationName, position in pairs(settings.Locations) do
            -- Create teleport button for this location
        end
    end
end
]]

-- Usage examples:
--[[
-- Start a custom module
_G.RobloxScriptHub.ModuleSystem:StartModule("AutoRebirth")

-- Stop a custom module
_G.RobloxScriptHub.ModuleSystem:StopModule("AutoRebirth")

-- Check if module is active
local isActive = _G.RobloxScriptHub.ModuleSystem:IsModuleActive("AutoRebirth")

-- Access module settings
local settings = _G.RobloxScriptHub.Settings.ModuleSettings["AutoRebirth"]
settings.MinLevel = 150  -- Change minimum level for rebirth

-- Use teleport functions
local teleportModule = _G.RobloxScriptHub.ModuleSystem.ActiveModules["Teleport"]
if teleportModule then
    teleportModule.TeleportFunctions.Spawn()  -- Teleport to spawn
end
]]
