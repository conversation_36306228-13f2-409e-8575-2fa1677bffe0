--// Sigma Spy custom remote responces
--// The Return *table* will be unpacked for the responce
--// If the return spoof is a function, passed arguments will be also passed to the function

return {
	-- [game.ReplicatedStorage.Remotes.HelloWorld] = {
	-- 	Method = "FireServer",
	-- 	Return = {"Hello world from Sigma Spy!"}
	-- }
	-- [game.ReplicatedStorage.Remotes.DepsoIsCool] = {
	-- 	Method = "FireServer",
	-- 	Return = function(OriginalFunc, ...)
	--		return {"Depso", "is awesome!"}
	-- end
	-- }
}