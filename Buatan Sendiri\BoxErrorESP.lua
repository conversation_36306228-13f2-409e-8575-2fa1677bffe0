-- Box Error ESP Script
-- Mencari box dengan Error sound yang memiliki SoundId selain rbxassetid://9066167010
-- dan membuat ESP berupa text

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")

local LocalPlayer = Players.LocalPlayer
local Camera = Workspace.CurrentCamera

-- Konfigurasi ESP
local ESP_CONFIG = {
    ErrorBox = {
        TextColor = Color3.fromRGB(255, 0, 0), -- <PERSON>rah untuk error box
        TextSize = 16,
        Font = Enum.Font.SourceSansBold,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0), -- Hitam
        Enabled = true
    },
    CorrectBox = {
        TextColor = Color3.fromRGB(0, 255, 0), -- Hijau untuk correct box
        TextSize = 14,
        Font = Enum.Font.SourceSans,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0), -- Hitam
        Enabled = false -- Default disabled
    },
    OtherLocation = {
        TextColor = Color3.fromRGB(255, 255, 0), -- Kuning untuk lokasi lain
        TextSize = 14,
        Font = Enum.Font.SourceSans,
        Outline = true,
        OutlineColor = Color3.fromRGB(0, 0, 0), -- Hitam
        Enabled = false -- Default disabled
    },
    MaxDistance = 10000000 -- Jarak maksimal untuk menampilkan ESP
}

-- Target SoundId yang akan diabaikan
local IGNORED_SOUND_ID = "rbxassetid://9066167010"

-- Tabel untuk menyimpan ESP objects
local espObjects = {
    errorBoxes = {},
    correctBoxes = {},
    errorOpenings = {},
    correctOpenings = {},
    errorOthers = {},
    correctOthers = {}
}

-- Fungsi untuk membuat ESP text untuk error box
local function createErrorESPText(box, errorSound)
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "BoxErrorESP"
    billboardGui.Adornee = box
    billboardGui.Size = UDim2.new(0, 200, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.AlwaysOnTop = true
    billboardGui.Parent = box

    local textLabel = Instance.new("TextLabel")
    textLabel.Name = "ESPText"
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "ERROR BOX\nSoundId: " .. (errorSound.SoundId or "None")
    textLabel.TextColor3 = ESP_CONFIG.ErrorBox.TextColor
    textLabel.TextSize = ESP_CONFIG.ErrorBox.TextSize
    textLabel.Font = ESP_CONFIG.ErrorBox.Font
    textLabel.TextStrokeTransparency = ESP_CONFIG.ErrorBox.Outline and 0 or 1
    textLabel.TextStrokeColor3 = ESP_CONFIG.ErrorBox.OutlineColor
    textLabel.TextScaled = true
    textLabel.Parent = billboardGui

    return billboardGui
end

-- Fungsi untuk membuat ESP text untuk correct box
local function createCorrectESPText(box)
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "BoxCorrectESP"
    billboardGui.Adornee = box
    billboardGui.Size = UDim2.new(0, 150, 0, 40)
    billboardGui.StudsOffset = Vector3.new(0, 2, 0)
    billboardGui.AlwaysOnTop = true
    billboardGui.Parent = box

    local textLabel = Instance.new("TextLabel")
    textLabel.Name = "ESPText"
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "CORRECT BOX\n✓ No Error Sound"
    textLabel.TextColor3 = ESP_CONFIG.CorrectBox.TextColor
    textLabel.TextSize = ESP_CONFIG.CorrectBox.TextSize
    textLabel.Font = ESP_CONFIG.CorrectBox.Font
    textLabel.TextStrokeTransparency = ESP_CONFIG.CorrectBox.Outline and 0 or 1
    textLabel.TextStrokeColor3 = ESP_CONFIG.CorrectBox.OutlineColor
    textLabel.TextScaled = true
    textLabel.Parent = billboardGui

    return billboardGui
end

-- Fungsi untuk membuat ESP text untuk error opening
local function createErrorOpeningESPText(opening, errorSound)
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "OpeningErrorESP"
    billboardGui.Adornee = opening
    billboardGui.Size = UDim2.new(0, 200, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.AlwaysOnTop = true
    billboardGui.Parent = opening

    local textLabel = Instance.new("TextLabel")
    textLabel.Name = "ESPText"
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "ERROR OPENING\nSoundId: " .. (errorSound.SoundId or "None")
    textLabel.TextColor3 = ESP_CONFIG.ErrorBox.TextColor
    textLabel.TextSize = ESP_CONFIG.ErrorBox.TextSize
    textLabel.Font = ESP_CONFIG.ErrorBox.Font
    textLabel.TextStrokeTransparency = ESP_CONFIG.ErrorBox.Outline and 0 or 1
    textLabel.TextStrokeColor3 = ESP_CONFIG.ErrorBox.OutlineColor
    textLabel.TextScaled = true
    textLabel.Parent = billboardGui

    return billboardGui
end

-- Fungsi untuk membuat ESP text untuk correct opening
local function createCorrectOpeningESPText(opening)
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "OpeningCorrectESP"
    billboardGui.Adornee = opening
    billboardGui.Size = UDim2.new(0, 150, 0, 40)
    billboardGui.StudsOffset = Vector3.new(0, 2, 0)
    billboardGui.AlwaysOnTop = true
    billboardGui.Parent = opening

    local textLabel = Instance.new("TextLabel")
    textLabel.Name = "ESPText"
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "CORRECT OPENING\n✓ No Error Sound"
    textLabel.TextColor3 = ESP_CONFIG.CorrectBox.TextColor
    textLabel.TextSize = ESP_CONFIG.CorrectBox.TextSize
    textLabel.Font = ESP_CONFIG.CorrectBox.Font
    textLabel.TextStrokeTransparency = ESP_CONFIG.CorrectBox.Outline and 0 or 1
    textLabel.TextStrokeColor3 = ESP_CONFIG.CorrectBox.OutlineColor
    textLabel.TextScaled = true
    textLabel.Parent = billboardGui

    return billboardGui
end

-- Fungsi untuk membuat ESP text untuk error other location
local function createErrorOtherESPText(item, errorSound)
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "OtherErrorESP"
    billboardGui.Adornee = item
    billboardGui.Size = UDim2.new(0, 200, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 3, 0)
    billboardGui.AlwaysOnTop = true
    billboardGui.Parent = item

    local textLabel = Instance.new("TextLabel")
    textLabel.Name = "ESPText"
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "ERROR LOCATION\nType: " .. item.Name .. "\nSoundId: " .. (errorSound.SoundId or "None")
    textLabel.TextColor3 = ESP_CONFIG.OtherLocation.TextColor
    textLabel.TextSize = ESP_CONFIG.OtherLocation.TextSize
    textLabel.Font = ESP_CONFIG.OtherLocation.Font
    textLabel.TextStrokeTransparency = ESP_CONFIG.OtherLocation.Outline and 0 or 1
    textLabel.TextStrokeColor3 = ESP_CONFIG.OtherLocation.OutlineColor
    textLabel.TextScaled = true
    textLabel.Parent = billboardGui

    return billboardGui
end

-- Fungsi untuk membuat ESP text untuk correct other location
local function createCorrectOtherESPText(item)
    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Name = "OtherCorrectESP"
    billboardGui.Adornee = item
    billboardGui.Size = UDim2.new(0, 150, 0, 40)
    billboardGui.StudsOffset = Vector3.new(0, 2, 0)
    billboardGui.AlwaysOnTop = true
    billboardGui.Parent = item

    local textLabel = Instance.new("TextLabel")
    textLabel.Name = "ESPText"
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "CORRECT LOCATION\nType: " .. item.Name .. "\n✓ No Error Sound"
    textLabel.TextColor3 = ESP_CONFIG.OtherLocation.TextColor
    textLabel.TextSize = ESP_CONFIG.OtherLocation.TextSize
    textLabel.Font = ESP_CONFIG.OtherLocation.Font
    textLabel.TextStrokeTransparency = ESP_CONFIG.OtherLocation.Outline and 0 or 1
    textLabel.TextStrokeColor3 = ESP_CONFIG.OtherLocation.OutlineColor
    textLabel.TextScaled = true
    textLabel.Parent = billboardGui

    return billboardGui
end

-- Fungsi untuk mengecek apakah box memiliki Error sound dengan SoundId yang valid
local function hasValidErrorSound(box)
    local errorSound = box:FindFirstChild("Error")
    if errorSound and errorSound:IsA("Sound") then
        local soundId = errorSound.SoundId
        -- Cek apakah SoundId bukan yang diabaikan dan tidak kosong
        if soundId and soundId ~= "" and soundId ~= IGNORED_SOUND_ID then
            return true, errorSound
        end
    end
    return false, nil
end

-- Fungsi untuk mengecek apakah item adalah correct (tidak memiliki Error sound)
local function isCorrectItem(item)
    local errorSound = item:FindFirstChild("Error")
    return not (errorSound and errorSound:IsA("Sound"))
end

-- Fungsi untuk mengecek jenis item
local function getItemType(item)
    local itemName = item.Name:lower()
    if itemName:find("box") then
        return "box"
    elseif itemName:find("opening") then
        return "opening"
    else
        return "other"
    end
end

-- Fungsi untuk menghitung jarak antara player dan box
local function getDistance(box)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        return math.huge
    end
    
    local playerPosition = LocalPlayer.Character.HumanoidRootPart.Position
    local boxPosition = box.Position
    return (playerPosition - boxPosition).Magnitude
end

-- Fungsi untuk memperbarui ESP berdasarkan jarak
local function updateESPVisibility()
    -- Update error boxes ESP
    for box, espGui in pairs(espObjects.errorBoxes) do
        if box.Parent and espGui.Parent then
            local distance = getDistance(box)
            if distance <= ESP_CONFIG.MaxDistance and ESP_CONFIG.ErrorBox.Enabled then
                espGui.Enabled = true
                -- Update text dengan jarak
                local textLabel = espGui:FindFirstChild("ESPText")
                if textLabel then
                    local errorSound = box:FindFirstChild("Error")
                    local soundId = errorSound and errorSound.SoundId or "None"
                    textLabel.Text = string.format("ERROR BOX\nSoundId: %s\nDistance: %.1fm", soundId, distance)
                end
            else
                espGui.Enabled = false
            end
        else
            -- Hapus ESP jika box sudah tidak ada
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.errorBoxes[box] = nil
        end
    end

    -- Update correct boxes ESP
    for box, espGui in pairs(espObjects.correctBoxes) do
        if box.Parent and espGui.Parent then
            local distance = getDistance(box)
            if distance <= ESP_CONFIG.MaxDistance and ESP_CONFIG.CorrectBox.Enabled then
                espGui.Enabled = true
                -- Update text dengan jarak
                local textLabel = espGui:FindFirstChild("ESPText")
                if textLabel then
                    textLabel.Text = string.format("CORRECT BOX\n✓ No Error Sound\nDistance: %.1fm", distance)
                end
            else
                espGui.Enabled = false
            end
        else
            -- Hapus ESP jika box sudah tidak ada
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.correctBoxes[box] = nil
        end
    end

    -- Update error openings ESP
    for opening, espGui in pairs(espObjects.errorOpenings) do
        if opening.Parent and espGui.Parent then
            local distance = getDistance(opening)
            if distance <= ESP_CONFIG.MaxDistance and ESP_CONFIG.ErrorBox.Enabled then
                espGui.Enabled = true
                -- Update text dengan jarak
                local textLabel = espGui:FindFirstChild("ESPText")
                if textLabel then
                    local errorSound = opening:FindFirstChild("Error")
                    local soundId = errorSound and errorSound.SoundId or "None"
                    textLabel.Text = string.format("ERROR OPENING\nSoundId: %s\nDistance: %.1fm", soundId, distance)
                end
            else
                espGui.Enabled = false
            end
        else
            -- Hapus ESP jika opening sudah tidak ada
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.errorOpenings[opening] = nil
        end
    end

    -- Update correct openings ESP
    for opening, espGui in pairs(espObjects.correctOpenings) do
        if opening.Parent and espGui.Parent then
            local distance = getDistance(opening)
            if distance <= ESP_CONFIG.MaxDistance and ESP_CONFIG.CorrectBox.Enabled then
                espGui.Enabled = true
                -- Update text dengan jarak
                local textLabel = espGui:FindFirstChild("ESPText")
                if textLabel then
                    textLabel.Text = string.format("CORRECT OPENING\n✓ No Error Sound\nDistance: %.1fm", distance)
                end
            else
                espGui.Enabled = false
            end
        else
            -- Hapus ESP jika opening sudah tidak ada
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.correctOpenings[opening] = nil
        end
    end

    -- Update error others ESP
    for item, espGui in pairs(espObjects.errorOthers) do
        if item.Parent and espGui.Parent then
            local distance = getDistance(item)
            if distance <= ESP_CONFIG.MaxDistance and ESP_CONFIG.OtherLocation.Enabled then
                espGui.Enabled = true
                -- Update text dengan jarak
                local textLabel = espGui:FindFirstChild("ESPText")
                if textLabel then
                    local errorSound = item:FindFirstChild("Error")
                    local soundId = errorSound and errorSound.SoundId or "None"
                    textLabel.Text = string.format("ERROR LOCATION\nType: %s\nSoundId: %s\nDistance: %.1fm", item.Name, soundId, distance)
                end
            else
                espGui.Enabled = false
            end
        else
            -- Hapus ESP jika item sudah tidak ada
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.errorOthers[item] = nil
        end
    end

    -- Update correct others ESP
    for item, espGui in pairs(espObjects.correctOthers) do
        if item.Parent and espGui.Parent then
            local distance = getDistance(item)
            if distance <= ESP_CONFIG.MaxDistance and ESP_CONFIG.OtherLocation.Enabled then
                espGui.Enabled = true
                -- Update text dengan jarak
                local textLabel = espGui:FindFirstChild("ESPText")
                if textLabel then
                    textLabel.Text = string.format("CORRECT LOCATION\nType: %s\n✓ No Error Sound\nDistance: %.1fm", item.Name, distance)
                end
            else
                espGui.Enabled = false
            end
        else
            -- Hapus ESP jika item sudah tidak ada
            if espGui.Parent then
                espGui:Destroy()
            end
            espObjects.correctOthers[item] = nil
        end
    end
end

-- Fungsi untuk scan semua box di workspace.Boxes
local function scanBoxes()
    local boxesFolder = Workspace:FindFirstChild("Boxes")
    if not boxesFolder then
        warn("Folder 'Boxes' tidak ditemukan di Workspace!")
        return
    end

    local foundErrorBoxes = 0
    local foundCorrectBoxes = 0
    local foundErrorOpenings = 0
    local foundCorrectOpenings = 0

    for _, item in pairs(boxesFolder:GetChildren()) do
        local itemType = getItemType(item)

        if itemType == "box" then
            -- Cek error boxes
            if not espObjects.errorBoxes[item] then
                local hasValidSound, errorSound = hasValidErrorSound(item)
                if hasValidSound then
                    local espGui = createErrorESPText(item, errorSound)
                    espObjects.errorBoxes[item] = espGui
                    foundErrorBoxes = foundErrorBoxes + 1
                    print(string.format("Error ESP dibuat untuk box: %s dengan SoundId: %s",
                        item.Name, errorSound.SoundId))
                end
            end

            -- Cek correct boxes
            if not espObjects.correctBoxes[item] then
                if isCorrectItem(item) then
                    local espGui = createCorrectESPText(item)
                    espObjects.correctBoxes[item] = espGui
                    foundCorrectBoxes = foundCorrectBoxes + 1
                    print(string.format("Correct ESP dibuat untuk box: %s", item.Name))
                end
            end
        elseif itemType == "opening" then
            -- Cek error openings
            if not espObjects.errorOpenings[item] then
                local hasValidSound, errorSound = hasValidErrorSound(item)
                if hasValidSound then
                    local espGui = createErrorOpeningESPText(item, errorSound)
                    espObjects.errorOpenings[item] = espGui
                    foundErrorOpenings = foundErrorOpenings + 1
                    print(string.format("Error ESP dibuat untuk opening: %s dengan SoundId: %s",
                        item.Name, errorSound.SoundId))
                end
            end

            -- Cek correct openings
            if not espObjects.correctOpenings[item] then
                if isCorrectItem(item) then
                    local espGui = createCorrectOpeningESPText(item)
                    espObjects.correctOpenings[item] = espGui
                    foundCorrectOpenings = foundCorrectOpenings + 1
                    print(string.format("Correct ESP dibuat untuk opening: %s", item.Name))
                end
            end
        else
            -- Cek error other locations
            if not espObjects.errorOthers[item] then
                local hasValidSound, errorSound = hasValidErrorSound(item)
                if hasValidSound then
                    local espGui = createErrorOtherESPText(item, errorSound)
                    espObjects.errorOthers[item] = espGui
                    foundErrorOpenings = foundErrorOpenings + 1
                    print(string.format("Error ESP dibuat untuk location: %s (Type: %s) dengan SoundId: %s",
                        item.Name, item.ClassName, errorSound.SoundId))
                end
            end

            -- Cek correct other locations
            if not espObjects.correctOthers[item] then
                if isCorrectItem(item) then
                    local espGui = createCorrectOtherESPText(item)
                    espObjects.correctOthers[item] = espGui
                    foundCorrectOpenings = foundCorrectOpenings + 1
                    print(string.format("Correct ESP dibuat untuk location: %s (Type: %s)",
                        item.Name, item.ClassName))
                end
            end
        end
    end

    if foundErrorBoxes > 0 then
        print(string.format("Ditemukan %d box dengan Error sound yang valid!", foundErrorBoxes))
    end
    if foundCorrectBoxes > 0 then
        print(string.format("Ditemukan %d correct box!", foundCorrectBoxes))
    end
    if foundErrorOpenings > 0 then
        print(string.format("Ditemukan %d opening/location dengan Error sound yang valid!", foundErrorOpenings))
    end
    if foundCorrectOpenings > 0 then
        print(string.format("Ditemukan %d correct opening/location!", foundCorrectOpenings))
    end
end

-- Fungsi untuk menghapus semua ESP
local function clearAllESP()
    -- Clear error boxes ESP
    for box, espGui in pairs(espObjects.errorBoxes) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.errorBoxes = {}

    -- Clear correct boxes ESP
    for box, espGui in pairs(espObjects.correctBoxes) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.correctBoxes = {}

    -- Clear error openings ESP
    for opening, espGui in pairs(espObjects.errorOpenings) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.errorOpenings = {}

    -- Clear correct openings ESP
    for opening, espGui in pairs(espObjects.correctOpenings) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.correctOpenings = {}

    -- Clear error others ESP
    for item, espGui in pairs(espObjects.errorOthers) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.errorOthers = {}

    -- Clear correct others ESP
    for item, espGui in pairs(espObjects.correctOthers) do
        if espGui.Parent then
            espGui:Destroy()
        end
    end
    espObjects.correctOthers = {}

    print("Semua ESP telah dihapus!")
end

-- Event listener untuk item baru yang ditambahkan (box, opening, atau location lain)
local function onItemAdded(item)
    wait(0.1) -- Tunggu sebentar untuk memastikan Error sound sudah ada

    local itemType = getItemType(item)

    if itemType == "box" then
        -- Cek error box
        local hasValidSound, errorSound = hasValidErrorSound(item)
        if hasValidSound then
            local espGui = createErrorESPText(item, errorSound)
            espObjects.errorBoxes[item] = espGui
            print(string.format("Error ESP otomatis dibuat untuk box baru: %s dengan SoundId: %s",
                item.Name, errorSound.SoundId))
        end

        -- Cek correct box
        if isCorrectItem(item) then
            local espGui = createCorrectESPText(item)
            espObjects.correctBoxes[item] = espGui
            print(string.format("Correct ESP otomatis dibuat untuk box baru: %s", item.Name))
        end
    elseif itemType == "opening" then
        -- Cek error opening
        local hasValidSound, errorSound = hasValidErrorSound(item)
        if hasValidSound then
            local espGui = createErrorOpeningESPText(item, errorSound)
            espObjects.errorOpenings[item] = espGui
            print(string.format("Error ESP otomatis dibuat untuk opening baru: %s dengan SoundId: %s",
                item.Name, errorSound.SoundId))
        end

        -- Cek correct opening
        if isCorrectItem(item) then
            local espGui = createCorrectOpeningESPText(item)
            espObjects.correctOpenings[item] = espGui
            print(string.format("Correct ESP otomatis dibuat untuk opening baru: %s", item.Name))
        end
    else
        -- Cek error other location
        local hasValidSound, errorSound = hasValidErrorSound(item)
        if hasValidSound then
            local espGui = createErrorOtherESPText(item, errorSound)
            espObjects.errorOthers[item] = espGui
            print(string.format("Error ESP otomatis dibuat untuk location baru: %s (Type: %s) dengan SoundId: %s",
                item.Name, item.ClassName, errorSound.SoundId))
        end

        -- Cek correct other location
        if isCorrectItem(item) then
            local espGui = createCorrectOtherESPText(item)
            espObjects.correctOthers[item] = espGui
            print(string.format("Correct ESP otomatis dibuat untuk location baru: %s (Type: %s)",
                item.Name, item.ClassName))
        end
    end
end

-- Event listener untuk item yang dihapus (box atau opening)
local function onItemRemoved(item)
    -- Remove error box ESP
    if espObjects.errorBoxes[item] then
        if espObjects.errorBoxes[item].Parent then
            espObjects.errorBoxes[item]:Destroy()
        end
        espObjects.errorBoxes[item] = nil
        print(string.format("Error ESP dihapus untuk box: %s", item.Name))
    end

    -- Remove correct box ESP
    if espObjects.correctBoxes[item] then
        if espObjects.correctBoxes[item].Parent then
            espObjects.correctBoxes[item]:Destroy()
        end
        espObjects.correctBoxes[item] = nil
        print(string.format("Correct ESP dihapus untuk box: %s", item.Name))
    end

    -- Remove error opening ESP
    if espObjects.errorOpenings[item] then
        if espObjects.errorOpenings[item].Parent then
            espObjects.errorOpenings[item]:Destroy()
        end
        espObjects.errorOpenings[item] = nil
        print(string.format("Error ESP dihapus untuk opening: %s", item.Name))
    end

    -- Remove correct opening ESP
    if espObjects.correctOpenings[item] then
        if espObjects.correctOpenings[item].Parent then
            espObjects.correctOpenings[item]:Destroy()
        end
        espObjects.correctOpenings[item] = nil
        print(string.format("Correct ESP dihapus untuk opening: %s", item.Name))
    end

    -- Remove error other ESP
    if espObjects.errorOthers[item] then
        if espObjects.errorOthers[item].Parent then
            espObjects.errorOthers[item]:Destroy()
        end
        espObjects.errorOthers[item] = nil
        print(string.format("Error ESP dihapus untuk location: %s", item.Name))
    end

    -- Remove correct other ESP
    if espObjects.correctOthers[item] then
        if espObjects.correctOthers[item].Parent then
            espObjects.correctOthers[item]:Destroy()
        end
        espObjects.correctOthers[item] = nil
        print(string.format("Correct ESP dihapus untuk location: %s", item.Name))
    end
end

-- Setup event listeners
local boxesFolder = Workspace:FindFirstChild("Boxes")
if boxesFolder then
    boxesFolder.ChildAdded:Connect(onItemAdded)
    boxesFolder.ChildRemoved:Connect(onItemRemoved)
end

-- Update ESP visibility setiap frame
local espUpdateConnection = RunService.Heartbeat:Connect(updateESPVisibility)

-- Scan awal
print("Memulai scan box dan opening dengan Error sound...")
scanBoxes()

-- Fungsi untuk toggle ESP (opsional)
local function toggleESP()
    ESP_CONFIG.ErrorBox.Enabled = not ESP_CONFIG.ErrorBox.Enabled
    ESP_CONFIG.CorrectBox.Enabled = not ESP_CONFIG.CorrectBox.Enabled

    -- Update visibility immediately
    updateESPVisibility()

    print(string.format("ESP toggled - Error: %s, Correct: %s",
        ESP_CONFIG.ErrorBox.Enabled and "ON" or "OFF",
        ESP_CONFIG.CorrectBox.Enabled and "ON" or "OFF"))
end

-- Fungsi untuk toggle hanya error ESP
local function toggleErrorESP()
    ESP_CONFIG.ErrorBox.Enabled = not ESP_CONFIG.ErrorBox.Enabled
    updateESPVisibility()
    print("Error ESP: " .. (ESP_CONFIG.ErrorBox.Enabled and "ON" or "OFF"))
end

-- Fungsi untuk toggle hanya correct ESP
local function toggleCorrectESP()
    ESP_CONFIG.CorrectBox.Enabled = not ESP_CONFIG.CorrectBox.Enabled
    updateESPVisibility()
    print("Correct ESP: " .. (ESP_CONFIG.CorrectBox.Enabled and "ON" or "OFF"))
end

-- Fungsi untuk toggle hanya other location ESP
local function toggleOtherESP()
    ESP_CONFIG.OtherLocation.Enabled = not ESP_CONFIG.OtherLocation.Enabled
    updateESPVisibility()
    print("Other Location ESP: " .. (ESP_CONFIG.OtherLocation.Enabled and "ON" or "OFF"))
end

-- Fungsi untuk teleport player ke posisi tertentu
local function teleportToPosition(position, itemName)
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        warn("Player character atau HumanoidRootPart tidak ditemukan!")
        return false
    end

    local humanoidRootPart = LocalPlayer.Character.HumanoidRootPart
    local offsetPosition = position + Vector3.new(0, 5, 0) -- Teleport 5 studs di atas item

    humanoidRootPart.CFrame = CFrame.new(offsetPosition)
    print(string.format("Teleported to: %s at position (%.1f, %.1f, %.1f)",
        itemName, offsetPosition.X, offsetPosition.Y, offsetPosition.Z))
    return true
end

-- Fungsi untuk mendapatkan item berikutnya dari kategori tertentu
local function getNextItemFromCategory(category)
    local items = {}
    local categoryData = espObjects[category]

    if not categoryData then return nil end

    -- Convert table to array for indexing
    for item, _ in pairs(categoryData) do
        if item.Parent then -- Make sure item still exists
            table.insert(items, item)
        end
    end

    if #items == 0 then return nil end

    -- Get current index and wrap around if necessary
    local currentIndex = currentTeleportIndex[category]
    if currentIndex > #items then
        currentIndex = 1
        currentTeleportIndex[category] = 1
    end

    local selectedItem = items[currentIndex]
    currentTeleportIndex[category] = currentIndex + 1

    return selectedItem
end

-- Fungsi untuk auto teleport cycle
local function autoTeleportCycle()
    if not autoTeleportEnabled then return end

    -- Priority order: errorBoxes -> errorOpenings -> errorOthers -> correctBoxes -> correctOpenings -> correctOthers
    local categories = {"errorBoxes", "errorOpenings", "errorOthers", "correctBoxes", "correctOpenings", "correctOthers"}

    for _, category in ipairs(categories) do
        local item = getNextItemFromCategory(category)
        if item then
            local position = item.Position
            local categoryName = category:gsub("([A-Z])", " %1"):gsub("^%l", string.upper) -- Format category name
            if teleportToPosition(position, item.Name .. " (" .. categoryName .. ")") then
                return -- Successfully teleported, exit function
            end
        end
    end

    print("No items available for teleportation!")
end

-- Fungsi untuk teleport ke kategori spesifik
local function teleportToCategory(category)
    local item = getNextItemFromCategory(category)
    if item then
        local position = item.Position
        local categoryName = category:gsub("([A-Z])", " %1"):gsub("^%l", string.upper)
        teleportToPosition(position, item.Name .. " (" .. categoryName .. ")")
    else
        print("No items found in category: " .. category)
    end
end

-- Fungsi untuk toggle auto teleport
local function toggleAutoTeleport()
    autoTeleportEnabled = not autoTeleportEnabled

    if autoTeleportEnabled then
        print("Auto Teleport: ON (Delay: " .. teleportDelay .. "s)")
        teleportConnection = spawn(function()
            while autoTeleportEnabled do
                autoTeleportCycle()
                wait(teleportDelay)
            end
        end)
    else
        print("Auto Teleport: OFF")
        if teleportConnection then
            teleportConnection = nil
        end
    end
end

-- Fungsi untuk mengatur delay teleport
local function setTeleportDelay(delay)
    teleportDelay = math.max(0.5, math.min(10, delay)) -- Clamp between 0.5 and 10 seconds
    print("Teleport delay set to: " .. teleportDelay .. "s")
end

-- GUI Variables
local gui = nil
local isGuiVisible = true

-- Auto Teleport Variables
local autoTeleportEnabled = false
local teleportDelay = 0 -- seconds
local currentTeleportIndex = {
    errorBoxes = 1,
    correctBoxes = 1,
    errorOpenings = 1,
    correctOpenings = 1,
    errorOthers = 1,
    correctOthers = 1
}
local teleportConnection = nil

-- Fungsi untuk menghitung total item di workspace.Boxes
local function getTotalCounts()
    local boxesFolder = Workspace:FindFirstChild("Boxes")
    if not boxesFolder then
        return 0, 0, 0, 0
    end

    local totalBoxes = 0
    local totalOpenings = 0
    local totalOthers = 0
    local totalItems = 0

    for _, item in pairs(boxesFolder:GetChildren()) do
        totalItems = totalItems + 1
        local itemType = getItemType(item)
        if itemType == "box" then
            totalBoxes = totalBoxes + 1
        elseif itemType == "opening" then
            totalOpenings = totalOpenings + 1
        else
            totalOthers = totalOthers + 1
        end
    end

    return totalBoxes, totalOpenings, totalOthers, totalItems
end

-- Fungsi untuk membuat GUI
local function createGUI()
    -- Main ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BoxErrorESPGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = LocalPlayer:WaitForChild("PlayerGui")

    -- Main Frame
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 320, 0, 520)
    mainFrame.Position = UDim2.new(0, 10, 0, 10)
    mainFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    mainFrame.BorderSizePixel = 0
    mainFrame.Active = true
    mainFrame.Draggable = true
    mainFrame.Parent = screenGui

    -- Corner untuk main frame
    local mainCorner = Instance.new("UICorner")
    mainCorner.CornerRadius = UDim.new(0, 8)
    mainCorner.Parent = mainFrame

    -- Title Bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 30)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainFrame

    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 8)
    titleCorner.Parent = titleBar

    -- Title Text
    local titleText = Instance.new("TextLabel")
    titleText.Name = "TitleText"
    titleText.Size = UDim2.new(1, -60, 1, 0)
    titleText.Position = UDim2.new(0, 10, 0, 0)
    titleText.BackgroundTransparency = 1
    titleText.Text = "Box Error ESP"
    titleText.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleText.TextSize = 16
    titleText.Font = Enum.Font.SourceSansBold
    titleText.TextXAlignment = Enum.TextXAlignment.Left
    titleText.Parent = titleBar

    -- Close Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 25, 0, 25)
    closeButton.Position = UDim2.new(1, -30, 0, 2.5)
    closeButton.BackgroundColor3 = Color3.fromRGB(255, 60, 60)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextSize = 14
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = titleBar

    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 4)
    closeCorner.Parent = closeButton

    -- Content Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Name = "ContentFrame"
    contentFrame.Size = UDim2.new(1, -20, 1, -40)
    contentFrame.Position = UDim2.new(0, 10, 0, 35)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Parent = mainFrame

    -- Status Label
    local statusLabel = Instance.new("TextLabel")
    statusLabel.Name = "StatusLabel"
    statusLabel.Size = UDim2.new(1, 0, 0, 30)
    statusLabel.Position = UDim2.new(0, 0, 0, 0)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Text = "Status: Ready"
    statusLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    statusLabel.TextSize = 14
    statusLabel.Font = Enum.Font.SourceSans
    statusLabel.TextXAlignment = Enum.TextXAlignment.Left
    statusLabel.Parent = contentFrame

    -- Found Items Label
    local foundLabel = Instance.new("TextLabel")
    foundLabel.Name = "FoundLabel"
    foundLabel.Size = UDim2.new(1, 0, 0, 60)
    foundLabel.Position = UDim2.new(0, 0, 0, 25)
    foundLabel.BackgroundTransparency = 1
    foundLabel.Text = "Error: 0 | Correct: 0\nBox: 0/0 | Opening: 0/0\nOther: 0/0 | Total: 0"
    foundLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    foundLabel.TextSize = 10
    foundLabel.Font = Enum.Font.SourceSans
    foundLabel.TextXAlignment = Enum.TextXAlignment.Left
    foundLabel.Parent = contentFrame

    return screenGui, mainFrame, statusLabel, foundLabel, closeButton, contentFrame
end

-- Fungsi untuk membuat button
local function createButton(parent, text, position, size, color, callback)
    local button = Instance.new("TextButton")
    button.Size = size or UDim2.new(1, 0, 0, 35)
    button.Position = position
    button.BackgroundColor3 = color or Color3.fromRGB(70, 70, 70)
    button.BorderSizePixel = 0
    button.Text = text
    button.TextColor3 = Color3.fromRGB(255, 255, 255)
    button.TextSize = 14
    button.Font = Enum.Font.SourceSans
    button.Parent = parent

    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = button

    -- Hover effect
    button.MouseEnter:Connect(function()
        button.BackgroundColor3 = Color3.fromRGB(90, 90, 90)
    end)

    button.MouseLeave:Connect(function()
        button.BackgroundColor3 = color or Color3.fromRGB(70, 70, 70)
    end)

    if callback then
        button.MouseButton1Click:Connect(callback)
    end

    return button
end

-- Fungsi untuk update GUI status
local function updateGUIStatus()
    if not gui then return end

    local statusLabel = gui:FindFirstChild("MainFrame"):FindFirstChild("ContentFrame"):FindFirstChild("StatusLabel")
    local foundLabel = gui:FindFirstChild("MainFrame"):FindFirstChild("ContentFrame"):FindFirstChild("FoundLabel")

    if statusLabel then
        local boxesFolder = Workspace:FindFirstChild("Boxes")
        if boxesFolder then
            statusLabel.Text = "Status: Active"
            statusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
        else
            statusLabel.Text = "Status: Boxes folder not found"
            statusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
        end
    end

    if foundLabel then
        local errorBoxCount = 0
        local correctBoxCount = 0
        local errorOpeningCount = 0
        local correctOpeningCount = 0
        local errorOtherCount = 0
        local correctOtherCount = 0

        for _ in pairs(espObjects.errorBoxes) do
            errorBoxCount = errorBoxCount + 1
        end
        for _ in pairs(espObjects.correctBoxes) do
            correctBoxCount = correctBoxCount + 1
        end
        for _ in pairs(espObjects.errorOpenings) do
            errorOpeningCount = errorOpeningCount + 1
        end
        for _ in pairs(espObjects.correctOpenings) do
            correctOpeningCount = correctOpeningCount + 1
        end
        for _ in pairs(espObjects.errorOthers) do
            errorOtherCount = errorOtherCount + 1
        end
        for _ in pairs(espObjects.correctOthers) do
            correctOtherCount = correctOtherCount + 1
        end

        -- Get total counts from workspace
        local totalBoxes, totalOpenings, totalOthers, totalItems = getTotalCounts()

        local totalError = errorBoxCount + errorOpeningCount + errorOtherCount
        local totalCorrect = correctBoxCount + correctOpeningCount + correctOtherCount
        local totalESP = totalError + totalCorrect

        foundLabel.Text = string.format("ESP: %d/%d | Error: %d | Correct: %d\nBox: %d/%d | Opening: %d/%d\nOther: %d/%d | Total: %d",
            totalESP, totalItems, totalError, totalCorrect,
            errorBoxCount + correctBoxCount, totalBoxes,
            errorOpeningCount + correctOpeningCount, totalOpenings,
            errorOtherCount + correctOtherCount, totalOthers,
            totalItems)
    end
end

-- Fungsi untuk scan dengan update GUI
local function scanBoxesWithGUI()
    scanBoxes()
    updateGUIStatus()
end

-- Fungsi untuk clear dengan update GUI
local function clearAllESPWithGUI()
    clearAllESP()
    updateGUIStatus()
end

-- Fungsi untuk toggle ESP dengan update GUI
local function toggleESPWithGUI()
    toggleESP()
    updateGUIStatus()
end

-- Fungsi untuk setup GUI buttons
local function setupGUIButtons(contentFrame)
    -- Scan Button
    createButton(contentFrame, "🔍 Scan All Locations", UDim2.new(0, 0, 0, 90), UDim2.new(1, 0, 0, 25),
        Color3.fromRGB(100, 150, 255), scanBoxesWithGUI)

    -- ESP Control Section
    local espLabel = Instance.new("TextLabel")
    espLabel.Name = "ESPLabel"
    espLabel.Size = UDim2.new(1, 0, 0, 20)
    espLabel.Position = UDim2.new(0, 0, 0, 120)
    espLabel.BackgroundTransparency = 1
    espLabel.Text = "ESP Controls:"
    espLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    espLabel.TextSize = 11
    espLabel.Font = Enum.Font.SourceSansBold
    espLabel.TextXAlignment = Enum.TextXAlignment.Left
    espLabel.Parent = contentFrame

    -- Toggle Error ESP Button
    createButton(contentFrame, "❌ Error ESP", UDim2.new(0, 0, 0, 145), UDim2.new(0.32, 0, 0, 25),
        Color3.fromRGB(255, 100, 100), function()
            toggleErrorESP()
            updateGUIStatus()
        end)

    -- Toggle Correct ESP Button
    createButton(contentFrame, "✅ Correct ESP", UDim2.new(0.34, 0, 0, 145), UDim2.new(0.32, 0, 0, 25),
        Color3.fromRGB(100, 255, 100), function()
            toggleCorrectESP()
            updateGUIStatus()
        end)

    -- Toggle Other ESP Button
    createButton(contentFrame, "🌟 Other ESP", UDim2.new(0.68, 0, 0, 145), UDim2.new(0.32, 0, 0, 25),
        Color3.fromRGB(255, 255, 100), function()
            toggleOtherESP()
            updateGUIStatus()
        end)

    -- Toggle All ESP Button
    createButton(contentFrame, "👁️ Toggle All ESP", UDim2.new(0, 0, 0, 175), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(255, 200, 100), toggleESPWithGUI)

    -- Clear ESP Button
    createButton(contentFrame, "🗑️ Clear All ESP", UDim2.new(0.51, 0, 0, 175), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(255, 100, 100), clearAllESPWithGUI)

    -- Teleport Section
    local teleportLabel = Instance.new("TextLabel")
    teleportLabel.Name = "TeleportLabel"
    teleportLabel.Size = UDim2.new(1, 0, 0, 20)
    teleportLabel.Position = UDim2.new(0, 0, 0, 210)
    teleportLabel.BackgroundTransparency = 1
    teleportLabel.Text = "Teleport Controls:"
    teleportLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    teleportLabel.TextSize = 11
    teleportLabel.Font = Enum.Font.SourceSansBold
    teleportLabel.TextXAlignment = Enum.TextXAlignment.Left
    teleportLabel.Parent = contentFrame

    -- Auto Teleport Toggle
    createButton(contentFrame, "🚀 Auto Teleport", UDim2.new(0, 0, 0, 235), UDim2.new(1, 0, 0, 25),
        Color3.fromRGB(150, 100, 255), function()
            toggleAutoTeleport()
            updateGUIStatus()
        end)

    -- Teleport to Error Boxes
    createButton(contentFrame, "📦 Error Box", UDim2.new(0, 0, 0, 265), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(255, 80, 80), function()
            teleportToCategory("errorBoxes")
        end)

    -- Teleport to Correct Boxes
    createButton(contentFrame, "✅ Correct Box", UDim2.new(0.51, 0, 0, 265), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(80, 255, 80), function()
            teleportToCategory("correctBoxes")
        end)

    -- Teleport to Error Openings
    createButton(contentFrame, "🚪 Error Opening", UDim2.new(0, 0, 0, 295), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(255, 120, 80), function()
            teleportToCategory("errorOpenings")
        end)

    -- Teleport to Correct Openings
    createButton(contentFrame, "✅ Correct Opening", UDim2.new(0.51, 0, 0, 295), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(120, 255, 80), function()
            teleportToCategory("correctOpenings")
        end)

    -- Teleport to Error Others
    createButton(contentFrame, "🌟 Error Other", UDim2.new(0, 0, 0, 325), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(255, 255, 80), function()
            teleportToCategory("errorOthers")
        end)

    -- Teleport to Correct Others
    createButton(contentFrame, "✅ Correct Other", UDim2.new(0.51, 0, 0, 325), UDim2.new(0.49, 0, 0, 25),
        Color3.fromRGB(200, 255, 80), function()
            teleportToCategory("correctOthers")
        end)

    -- Settings Label
    local settingsLabel = Instance.new("TextLabel")
    settingsLabel.Name = "SettingsLabel"
    settingsLabel.Size = UDim2.new(1, 0, 0, 20)
    settingsLabel.Position = UDim2.new(0, 0, 0, 360)
    settingsLabel.BackgroundTransparency = 1
    settingsLabel.Text = "Settings:"
    settingsLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    settingsLabel.TextSize = 11
    settingsLabel.Font = Enum.Font.SourceSansBold
    settingsLabel.TextXAlignment = Enum.TextXAlignment.Left
    settingsLabel.Parent = contentFrame

    -- Max Distance Slider
    local distanceFrame = Instance.new("Frame")
    distanceFrame.Name = "DistanceFrame"
    distanceFrame.Size = UDim2.new(1, 0, 0, 35)
    distanceFrame.Position = UDim2.new(0, 0, 0, 385)
    distanceFrame.BackgroundTransparency = 1
    distanceFrame.Parent = contentFrame

    local distanceLabel = Instance.new("TextLabel")
    distanceLabel.Name = "DistanceLabel"
    distanceLabel.Size = UDim2.new(1, 0, 0, 20)
    distanceLabel.Position = UDim2.new(0, 0, 0, 0)
    distanceLabel.BackgroundTransparency = 1
    distanceLabel.Text = "Max Distance: " .. ESP_CONFIG.MaxDistance
    distanceLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    distanceLabel.TextSize = 12
    distanceLabel.Font = Enum.Font.SourceSans
    distanceLabel.TextXAlignment = Enum.TextXAlignment.Left
    distanceLabel.Parent = distanceFrame

    local distanceSlider = Instance.new("Frame")
    distanceSlider.Name = "DistanceSlider"
    distanceSlider.Size = UDim2.new(1, 0, 0, 15)
    distanceSlider.Position = UDim2.new(0, 0, 0, 20)
    distanceSlider.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    distanceSlider.BorderSizePixel = 0
    distanceSlider.Parent = distanceFrame

    local sliderCorner = Instance.new("UICorner")
    sliderCorner.CornerRadius = UDim.new(0, 10)
    sliderCorner.Parent = distanceSlider

    local sliderButton = Instance.new("TextButton")
    sliderButton.Name = "SliderButton"
    sliderButton.Size = UDim2.new(0, 15, 1, 0)
    sliderButton.Position = UDim2.new(ESP_CONFIG.MaxDistance / 2000, -7.5, 0, 0)
    sliderButton.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    sliderButton.BorderSizePixel = 0
    sliderButton.Text = ""
    sliderButton.Parent = distanceSlider

    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UDim.new(0, 10)
    buttonCorner.Parent = sliderButton

    -- Slider functionality
    local dragging = false
    sliderButton.MouseButton1Down:Connect(function()
        dragging = true
    end)

    game:GetService("UserInputService").InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)

    game:GetService("UserInputService").InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local mouse = LocalPlayer:GetMouse()
            local sliderPos = distanceSlider.AbsolutePosition
            local sliderSize = distanceSlider.AbsoluteSize
            local relativeX = math.clamp((mouse.X - sliderPos.X) / sliderSize.X, 0, 1)

            sliderButton.Position = UDim2.new(relativeX, -7.5, 0, 0)
            ESP_CONFIG.MaxDistance = math.floor(relativeX * 2000)
            distanceLabel.Text = "Max Distance: " .. ESP_CONFIG.MaxDistance
        end
    end)

    -- Teleport Delay Slider
    local teleportDelayFrame = Instance.new("Frame")
    teleportDelayFrame.Name = "TeleportDelayFrame"
    teleportDelayFrame.Size = UDim2.new(1, 0, 0, 35)
    teleportDelayFrame.Position = UDim2.new(0, 0, 0, 425)
    teleportDelayFrame.BackgroundTransparency = 1
    teleportDelayFrame.Parent = contentFrame

    local teleportDelayLabel = Instance.new("TextLabel")
    teleportDelayLabel.Name = "TeleportDelayLabel"
    teleportDelayLabel.Size = UDim2.new(1, 0, 0, 15)
    teleportDelayLabel.Position = UDim2.new(0, 0, 0, 0)
    teleportDelayLabel.BackgroundTransparency = 1
    teleportDelayLabel.Text = "Teleport Delay: " .. teleportDelay .. "s"
    teleportDelayLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    teleportDelayLabel.TextSize = 10
    teleportDelayLabel.Font = Enum.Font.SourceSans
    teleportDelayLabel.TextXAlignment = Enum.TextXAlignment.Left
    teleportDelayLabel.Parent = teleportDelayFrame

    local teleportDelaySlider = Instance.new("Frame")
    teleportDelaySlider.Name = "TeleportDelaySlider"
    teleportDelaySlider.Size = UDim2.new(1, 0, 0, 15)
    teleportDelaySlider.Position = UDim2.new(0, 0, 0, 20)
    teleportDelaySlider.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    teleportDelaySlider.BorderSizePixel = 0
    teleportDelaySlider.Parent = teleportDelayFrame

    local teleportDelayCorner = Instance.new("UICorner")
    teleportDelayCorner.CornerRadius = UDim.new(0, 7)
    teleportDelayCorner.Parent = teleportDelaySlider

    local teleportDelayButton = Instance.new("TextButton")
    teleportDelayButton.Name = "TeleportDelayButton"
    teleportDelayButton.Size = UDim2.new(0, 15, 1, 0)
    teleportDelayButton.Position = UDim2.new((teleportDelay - 0.5) / 9.5, -7.5, 0, 0) -- Map 0.5-10 to 0-1
    teleportDelayButton.BackgroundColor3 = Color3.fromRGB(150, 100, 255)
    teleportDelayButton.BorderSizePixel = 0
    teleportDelayButton.Text = ""
    teleportDelayButton.Parent = teleportDelaySlider

    local teleportDelayButtonCorner = Instance.new("UICorner")
    teleportDelayButtonCorner.CornerRadius = UDim.new(0, 7)
    teleportDelayButtonCorner.Parent = teleportDelayButton

    -- Teleport Delay Slider functionality
    local teleportDragging = false
    teleportDelayButton.MouseButton1Down:Connect(function()
        teleportDragging = true
    end)

    game:GetService("UserInputService").InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            teleportDragging = false
        end
    end)

    game:GetService("UserInputService").InputChanged:Connect(function(input)
        if teleportDragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local mouse = LocalPlayer:GetMouse()
            local sliderPos = teleportDelaySlider.AbsolutePosition
            local sliderSize = teleportDelaySlider.AbsoluteSize
            local relativeX = math.clamp((mouse.X - sliderPos.X) / sliderSize.X, 0, 1)

            teleportDelayButton.Position = UDim2.new(relativeX, -7.5, 0, 0)
            local newDelay = 0.5 + (relativeX * 9.5) -- Map 0-1 to 0.5-10
            setTeleportDelay(newDelay)
            teleportDelayLabel.Text = "Teleport Delay: " .. teleportDelay .. "s"
        end
    end)
end

-- Initialize GUI
local function initializeGUI()
    local screenGui, mainFrame, statusLabel, foundLabel, closeButton, contentFrame = createGUI()
    gui = screenGui

    -- Setup buttons
    setupGUIButtons(contentFrame)

    -- Close button functionality
    closeButton.MouseButton1Click:Connect(function()
        gui:Destroy()
        gui = nil
        isGuiVisible = false
    end)

    -- Update status initially
    updateGUIStatus()

    -- Update status every 1 second for real-time updates
    spawn(function()
        while gui and gui.Parent do
            wait(1)
            updateGUIStatus()
        end
    end)
end

-- Commands untuk kontrol ESP (bisa dipanggil dari console)
_G.BoxErrorESP = {
    scan = scanBoxes,
    clear = clearAllESP,
    toggle = toggleESP,
    toggleError = toggleErrorESP,
    toggleCorrect = toggleCorrectESP,
    toggleOther = toggleOtherESP,
    config = ESP_CONFIG,
    objects = espObjects,
    getTotalCounts = getTotalCounts,
    -- Teleport functions
    toggleAutoTeleport = toggleAutoTeleport,
    teleportToErrorBoxes = function() teleportToCategory("errorBoxes") end,
    teleportToCorrectBoxes = function() teleportToCategory("correctBoxes") end,
    teleportToErrorOpenings = function() teleportToCategory("errorOpenings") end,
    teleportToCorrectOpenings = function() teleportToCategory("correctOpenings") end,
    teleportToErrorOthers = function() teleportToCategory("errorOthers") end,
    teleportToCorrectOthers = function() teleportToCategory("correctOthers") end,
    setTeleportDelay = setTeleportDelay,
    showGUI = function()
        if not gui then
            initializeGUI()
            isGuiVisible = true
        end
    end,
    hideGUI = function()
        if gui then
            gui:Destroy()
            gui = nil
            isGuiVisible = false
        end
    end
}

-- Initialize GUI on start
initializeGUI()

print("Box Error ESP Script with GUI loaded!")
print("Commands:")
print("_G.BoxErrorESP.scan() - Scan ulang semua locations")
print("_G.BoxErrorESP.clear() - Hapus semua ESP")
print("_G.BoxErrorESP.toggle() - Toggle visibility semua ESP")
print("_G.BoxErrorESP.toggleError() - Toggle hanya Error ESP")
print("_G.BoxErrorESP.toggleCorrect() - Toggle hanya Correct ESP")
print("_G.BoxErrorESP.toggleOther() - Toggle hanya Other Location ESP")
print("_G.BoxErrorESP.showGUI() - Tampilkan GUI")
print("_G.BoxErrorESP.hideGUI() - Sembunyikan GUI")
print("")
print("Teleport Commands:")
print("_G.BoxErrorESP.toggleAutoTeleport() - Toggle auto teleport")
print("_G.BoxErrorESP.teleportToErrorBoxes() - Teleport ke error box")
print("_G.BoxErrorESP.teleportToCorrectBoxes() - Teleport ke correct box")
print("_G.BoxErrorESP.teleportToErrorOpenings() - Teleport ke error opening")
print("_G.BoxErrorESP.teleportToCorrectOpenings() - Teleport ke correct opening")
print("_G.BoxErrorESP.teleportToErrorOthers() - Teleport ke error other")
print("_G.BoxErrorESP.teleportToCorrectOthers() - Teleport ke correct other")
print("_G.BoxErrorESP.setTeleportDelay(seconds) - Set teleport delay (0.5-10s)")
print("")
print("ESP Types:")
print("- Error Items (Red): Item dengan Error sound selain rbxassetid://9066167010")
print("- Correct Items (Green): Item tanpa Error sound")
print("- Other Locations (Yellow): Item selain Box/Opening")
print("")
print("Supported Items:")
print("- Box: Item dengan nama mengandung 'box'")
print("- Opening: Item dengan nama mengandung 'opening'")
print("- Other: Semua item lain di workspace.Boxes")
print("")
print("Auto Teleport: Cycles through Error Boxes -> Error Openings -> Error Others -> Correct Boxes -> Correct Openings -> Correct Others")
