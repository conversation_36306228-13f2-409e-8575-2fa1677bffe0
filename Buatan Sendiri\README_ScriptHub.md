# 🚀 Roblox Script Hub - Modular Edition

Script hub Roblox yang dibuat dengan arsitektur modular untuk memudahkan ekspansi fitur. Base GUI terinspirasi dari `AutoClickUGC2.lua` dengan sistem modul yang fleksibel.

## ✨ Fitur Utama

### 🏠 Home Tab
- Dashboard utama dengan informasi selamat datang
- Monitor modul aktif secara real-time
- Statistik penggunaan script hub

### 🖱️ AutoClick Module
- Auto click otomatis dengan interval yang dapat disesuaikan
- Mendukung berbagai remote event path
- Kontrol start/stop dengan toggle yang smooth

### 🌾 AutoFarm Module
- Auto farming untuk coins, gems, dan XP
- Sistem touch interest untuk mengumpulkan item
- Interval farming yang dapat dikonfigurasi

### 👤 Player Module
- Pengaturan WalkSpeed dan JumpPower
- NoClip mode untuk melewati dinding
- Infinite Jump untuk melompat tanpa batas

### ⚙️ Settings Tab
- Pengaturan transparansi UI
- Informasi versi dan credits
- Konfigurasi global script hub

## 🎨 Desain UI

### Theme Colors (Netflix-inspired)
- **Primary Red**: `Color3.fromRGB(229, 9, 20)`
- **Background Black**: `Color3.fromRGB(20, 20, 20)`
- **Dark Gray**: `Color3.fromRGB(35, 35, 35)`
- **Light Gray**: `Color3.fromRGB(70, 70, 70)`
- **White**: `Color3.fromRGB(255, 255, 255)`
- **Gray Blue**: `Color3.fromRGB(30, 35, 45)`

### Fitur UI
- **Draggable Window**: Dapat dipindahkan dengan drag title bar
- **Minimize/Close**: Tombol minimize dan close yang responsif
- **Smooth Animations**: Menggunakan TweenService untuk animasi halus
- **Responsive Layout**: Layout yang menyesuaikan dengan konten
- **Scrollable Content**: Area konten dapat di-scroll jika diperlukan

## 🔧 Arsitektur Modular

### Module System
Script hub menggunakan sistem modul yang memungkinkan penambahan fitur baru dengan mudah:

```lua
-- Contoh struktur modul
local MyModule = {
    Name = "MyModule",
    DisplayName = "My Custom Module",
    Description = "Description of what this module does",
    DefaultSettings = {
        Enabled = false,
        CustomSetting = "value"
    },
    
    Start = function(settings)
        -- Logic untuk memulai modul
        return moduleData
    end,
    
    Stop = function(moduleData)
        -- Logic untuk menghentikan modul
    end
}

-- Registrasi modul
ModuleSystem:RegisterModule(MyModule)
```

### Global Access
Script hub dapat diakses secara global melalui `_G.RobloxScriptHub`:

```lua
-- Akses module system
_G.RobloxScriptHub.ModuleSystem

-- Akses settings
_G.RobloxScriptHub.Settings

-- Registrasi modul baru
_G.RobloxScriptHub.RegisterModule(moduleData)
```

## 📋 Cara Penggunaan

### 1. Menjalankan Script
```lua
-- Copy dan paste seluruh kode RobloxScriptHub.lua ke executor
-- Script akan otomatis menginisialisasi dan menampilkan GUI
```

### 2. Navigasi Tab
- Klik tab di menu sebelah kiri untuk berpindah antar fitur
- Tab yang aktif akan ditandai dengan warna merah

### 3. Mengaktifkan Modul
- Masuk ke tab modul yang diinginkan (AutoClick, AutoFarm, Player)
- Klik toggle switch untuk mengaktifkan/menonaktifkan modul
- Status akan berubah dari "Idle" ke "Active"

### 4. Konfigurasi Settings
- Setiap modul memiliki pengaturan khusus
- AutoClick: Atur interval clicking
- Player: Atur speed, jump power, dan fitur khusus
- Settings: Atur transparansi UI

## 🛠️ Kustomisasi

### Menambah Modul Baru
1. Buat struktur modul sesuai template
2. Implementasikan fungsi `Start` dan `Stop`
3. Registrasi modul dengan `ModuleSystem:RegisterModule()`
4. Tambahkan tab baru jika diperlukan

### Modifikasi Theme
Ubah nilai di object `THEME` untuk mengubah skema warna:

```lua
local THEME = {
    RED = Color3.fromRGB(229, 9, 20),    -- Warna utama
    BLACK = Color3.fromRGB(20, 20, 20),  -- Background
    -- ... warna lainnya
}
```

### Pengaturan Default
Modifikasi object `Settings` untuk mengubah konfigurasi default:

```lua
local Settings = {
    Transparency = 0.2,           -- Transparansi UI
    UISize = UDim2.new(0, 600, 0, 400),  -- Ukuran window
    ActiveTab = "Home",           -- Tab default
    -- ... pengaturan lainnya
}
```

## 🔍 Troubleshooting

### Modul Tidak Berfungsi
1. Pastikan remote event path benar (untuk AutoClick)
2. Cek apakah game memiliki anti-cheat yang memblokir
3. Verifikasi bahwa executor mendukung fungsi yang digunakan

### UI Tidak Muncul
1. Pastikan script dijalankan di LocalScript environment
2. Cek apakah PlayerGui dapat diakses
3. Restart script jika diperlukan

### Performance Issues
1. Kurangi interval pada modul yang aktif
2. Nonaktifkan modul yang tidak digunakan
3. Tutup script hub jika tidak diperlukan

## 📝 Credits

- **Base GUI**: Terinspirasi dari `AutoClickUGC2.lua`
- **Architecture**: Modular design untuk fleksibilitas
- **Theme**: Netflix-inspired color scheme
- **Created**: Dengan sistem yang mudah diperluas

## 🔄 Version History

- **v1.0**: Initial release dengan 3 modul utama
  - AutoClick Module
  - AutoFarm Module  
  - Player Module
  - Modular architecture
  - Netflix-inspired UI theme

---

**Note**: Script ini dibuat untuk tujuan edukasi. Gunakan dengan bijak dan patuhi terms of service game yang dimainkan.
