--[[
    Roblox Script Hub Configuration File
    Customize settings, themes, and module configurations here
]]

local ScriptHubConfig = {}

-- UI Theme Configuration
ScriptHubConfig.Themes = {
    -- Default Netflix Theme
    Netflix = {
        RED = Color3.fromRGB(229, 9, 20),
        BLACK = Color3.fromRGB(20, 20, 20),
        DARK_GRAY = Color3.fromRGB(35, 35, 35),
        LIGHT_GRAY = Color3.fromRGB(70, 70, 70),
        WHITE = Color3.fromRGB(255, 255, 255),
        GRAY_BLUE = Color3.fromRGB(30, 35, 45),
        GREEN = Color3.fromRGB(46, 125, 50),
        ORANGE = Color3.fromRGB(255, 152, 0)
    },
    
    -- Discord Theme
    Discord = {
        RED = Color3.fromRGB(237, 66, 69),
        BLACK = Color3.fromRGB(35, 39, 42),
        DARK_GRAY = Color3.fromRGB(47, 49, 54),
        LIGHT_GRAY = Color3.fromRGB(114, 118, 125),
        WHITE = Color3.fromRGB(255, 255, 255),
        GRAY_BLUE = Color3.fromRGB(54, 57, 63),
        GREEN = Color3.fromRGB(67, 181, 129),
        ORANGE = Color3.fromRGB(250, 166, 26)
    },
    
    -- Ocean Theme
    Ocean = {
        RED = Color3.fromRGB(52, 152, 219),
        BLACK = Color3.fromRGB(44, 62, 80),
        DARK_GRAY = Color3.fromRGB(52, 73, 94),
        LIGHT_GRAY = Color3.fromRGB(149, 165, 166),
        WHITE = Color3.fromRGB(236, 240, 241),
        GRAY_BLUE = Color3.fromRGB(52, 73, 94),
        GREEN = Color3.fromRGB(46, 204, 113),
        ORANGE = Color3.fromRGB(230, 126, 34)
    },
    
    -- Dark Purple Theme
    Purple = {
        RED = Color3.fromRGB(155, 89, 182),
        BLACK = Color3.fromRGB(44, 44, 84),
        DARK_GRAY = Color3.fromRGB(74, 74, 124),
        LIGHT_GRAY = Color3.fromRGB(108, 122, 137),
        WHITE = Color3.fromRGB(245, 245, 245),
        GRAY_BLUE = Color3.fromRGB(84, 84, 134),
        GREEN = Color3.fromRGB(39, 174, 96),
        ORANGE = Color3.fromRGB(243, 156, 18)
    }
}

-- Default Settings Configuration
ScriptHubConfig.DefaultSettings = {
    -- UI Settings
    Transparency = 0.2,
    DragSpeed = 0.05,
    MinimizedState = false,
    ActiveTab = "Home",
    UISize = UDim2.new(0, 600, 0, 400),
    Theme = "Netflix", -- Choose from: Netflix, Discord, Ocean, Purple
    
    -- Window Settings
    WindowTitle = "🚀 Roblox Script Hub",
    WindowIcon = "🚀",
    
    -- Animation Settings
    AnimationSpeed = 0.2,
    SmoothScrolling = true,
    
    -- Performance Settings
    UpdateInterval = 0.1,
    MaxFPS = 60
}

-- Module Configurations
ScriptHubConfig.ModuleConfigs = {
    AutoClick = {
        DefaultInterval = 0.005,
        MaxInterval = 5.0,
        MinInterval = 0.001,
        CommonRemotePaths = {
            "ReplicatedStorage.Remotes.Click",
            "ReplicatedStorage.Events.Click",
            "ReplicatedStorage.Click",
            "Workspace.Events.Click"
        }
    },
    
    AutoFarm = {
        DefaultInterval = 0.1,
        CommonItemNames = {
            "Coin", "Coins", "Money",
            "Gem", "Gems", "Diamond",
            "XP", "XPorb", "Experience",
            "Token", "Tokens", "Point"
        },
        SearchFolders = {
            "Workspace.Coins",
            "Workspace.Gems",
            "Workspace.Items",
            "Workspace.Collectibles"
        }
    },
    
    Player = {
        DefaultWalkSpeed = 16,
        MaxWalkSpeed = 500,
        DefaultJumpPower = 50,
        MaxJumpPower = 500,
        SafeMode = true -- Prevents extreme values that might trigger anti-cheat
    }
}

-- Tab Configuration
ScriptHubConfig.Tabs = {
    {Name = "Home", Icon = "🏠", Order = 1, Enabled = true},
    {Name = "AutoClick", Icon = "🖱️", Order = 2, Enabled = true},
    {Name = "AutoFarm", Icon = "🌾", Order = 3, Enabled = true},
    {Name = "Player", Icon = "👤", Order = 4, Enabled = true},
    {Name = "Teleport", Icon = "🌐", Order = 5, Enabled = false}, -- Custom tab
    {Name = "Settings", Icon = "⚙️", Order = 6, Enabled = true}
}

-- Game-Specific Configurations
ScriptHubConfig.GameConfigs = {
    -- Pet Simulator X
    [2316994223] = {
        AutoClick = {
            RemotePath = "ReplicatedStorage.Network.Pets: Equip"
        },
        AutoFarm = {
            ItemNames = {"Coin", "Diamond", "Rainbow Coin"},
            FarmAreas = {"Spawn", "Town", "Beach"}
        }
    },
    
    -- Adopt Me
    [920587237] = {
        AutoFarm = {
            ItemNames = {"Money", "Bucks", "Star Rewards"},
            SafeMode = true
        }
    },
    
    -- Blox Fruits
    [2753915549] = {
        AutoFarm = {
            ItemNames = {"Money", "Fragment", "Exp"},
            RemotePaths = {"ReplicatedStorage.Remotes.CommF_"}
        }
    }
}

-- Security Settings
ScriptHubConfig.Security = {
    -- Anti-detection features
    RandomizeIntervals = true,
    HumanLikeDelays = true,
    MaxContinuousTime = 300, -- 5 minutes max continuous operation
    
    -- Safe mode settings
    SafeMode = true,
    MaxActionsPerSecond = 20,
    
    -- Whitelist/Blacklist
    WhitelistedGames = {}, -- Empty = all games allowed
    BlacklistedGames = {}, -- Games where script won't work
}

-- Notification Settings
ScriptHubConfig.Notifications = {
    Enabled = true,
    ShowStartup = true,
    ShowModuleStatus = true,
    ShowErrors = true,
    ShowWarnings = true,
    
    -- Notification style
    Position = "TopRight", -- TopRight, TopLeft, BottomRight, BottomLeft
    Duration = 3, -- seconds
    MaxNotifications = 5
}

-- Keybind Settings
ScriptHubConfig.Keybinds = {
    ToggleGUI = Enum.KeyCode.RightControl,
    EmergencyStop = Enum.KeyCode.F1,
    QuickToggleAutoClick = Enum.KeyCode.F2,
    QuickToggleAutoFarm = Enum.KeyCode.F3
}

-- Export configuration
ScriptHubConfig.GetTheme = function(themeName)
    return ScriptHubConfig.Themes[themeName] or ScriptHubConfig.Themes.Netflix
end

ScriptHubConfig.GetGameConfig = function(gameId)
    return ScriptHubConfig.GameConfigs[gameId] or {}
end

ScriptHubConfig.ApplyGameSpecificSettings = function(settings)
    local gameId = game.PlaceId
    local gameConfig = ScriptHubConfig.GetGameConfig(gameId)
    
    if gameConfig then
        -- Apply game-specific settings
        for moduleName, moduleConfig in pairs(gameConfig) do
            if settings.ModuleSettings[moduleName] then
                for setting, value in pairs(moduleConfig) do
                    settings.ModuleSettings[moduleName][setting] = value
                end
            end
        end
        
        print("Applied game-specific configuration for game ID: " .. gameId)
    end
end

-- Validation functions
ScriptHubConfig.ValidateSettings = function(settings)
    -- Validate transparency
    if settings.Transparency < 0 then settings.Transparency = 0 end
    if settings.Transparency > 1 then settings.Transparency = 1 end
    
    -- Validate UI size
    if settings.UISize.X.Offset < 400 then
        settings.UISize = UDim2.new(0, 400, settings.UISize.Y.Scale, settings.UISize.Y.Offset)
    end
    if settings.UISize.Y.Offset < 300 then
        settings.UISize = UDim2.new(settings.UISize.X.Scale, settings.UISize.X.Offset, 0, 300)
    end
    
    -- Validate module settings
    for moduleName, moduleSettings in pairs(settings.ModuleSettings) do
        local config = ScriptHubConfig.ModuleConfigs[moduleName]
        if config then
            -- Validate intervals
            if moduleSettings.Interval then
                if config.MinInterval and moduleSettings.Interval < config.MinInterval then
                    moduleSettings.Interval = config.MinInterval
                end
                if config.MaxInterval and moduleSettings.Interval > config.MaxInterval then
                    moduleSettings.Interval = config.MaxInterval
                end
            end
            
            -- Validate player settings
            if moduleName == "Player" and config.SafeMode then
                if moduleSettings.WalkSpeed and moduleSettings.WalkSpeed > config.MaxWalkSpeed then
                    moduleSettings.WalkSpeed = config.MaxWalkSpeed
                end
                if moduleSettings.JumpPower and moduleSettings.JumpPower > config.MaxJumpPower then
                    moduleSettings.JumpPower = config.MaxJumpPower
                end
            end
        end
    end
    
    return settings
end

-- Usage example:
--[[
-- In your main script, load this config:
local Config = loadstring(game:HttpGet("path/to/ScriptHubConfig.lua"))()

-- Apply theme
local selectedTheme = Config.GetTheme(Config.DefaultSettings.Theme)

-- Apply game-specific settings
Config.ApplyGameSpecificSettings(Settings)

-- Validate all settings
Settings = Config.ValidateSettings(Settings)
]]

return ScriptHubConfig
