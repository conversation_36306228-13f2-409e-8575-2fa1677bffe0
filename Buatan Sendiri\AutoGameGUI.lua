local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")

local THEME_RED = Color3.fromRGB(229, 9, 20)
local THEME_BLACK = Color3.fromRGB(20, 20, 20)
local THEME_DARK_GRAY = Color3.fromRGB(35, 35, 35)
local THEME_LIGHT_GRAY = Color3.fromRGB(70, 70, 70)
local THEME_WHITE = Color3.fromRGB(255, 255, 255)
local THEME_GRAY_BLUE = Color3.fromRGB(30, 35, 45)

local Settings = {
    AutoRoll = false,
    AutoSpin = false,
    AutoRebirth = false,
    AutoCollectCoins = false,
    AutoCollectGems = false,
    AutoCollectDirectCoins = false, -- For direct Coins in workspace
    AutoCollectXPorbs = false, -- For XPorbs in IslandXP folder
    RollInterval = 0.005, -- 0.005 seconds for ultra-fast rolling
    SpinInterval = 0.005, -- 0.005 seconds for ultra-fast spinning
    RebirthInterval = 0.005, -- 0.005 seconds for ultra-fast rebirthing
    CollectInterval = 0.005, -- 0.005 seconds for ultra-fast collecting
    SelectedReward = "Reward7",
    Transparency = 0.2,
    DragSpeed = 0.05,
    MinimizedState = false,
    ActiveTab = "Home",
    UISize = UDim2.new(0, 500, 0, 300)
}

local function CreateElement(className, properties, children)
    local element = Instance.new(className)

    for property, value in pairs(properties) do
        element[property] = value
    end

    if children then
        for _, child in pairs(children) do
            child.Parent = element
        end
    end

    return element
end

local function CreateGUI()
    local ScreenGui = CreateElement("ScreenGui", {
        Name = "AutoGameGUI",
        ResetOnSpawn = false,
        ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
        Parent = PlayerGui
    })

    local MainFrame = CreateElement("Frame", {
        Name = "MainFrame",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME_BLACK,
        BackgroundTransparency = Settings.Transparency,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 0, 0.5, 0),
        Size = Settings.UISize,
        Parent = ScreenGui
    })

    local UICorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = MainFrame
    })

    local TitleBar = CreateElement("Frame", {
        Name = "TitleBar",
        BackgroundColor3 = THEME_RED,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 40),
        Parent = MainFrame
    })

    local TitleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = TitleBar
    })

    local BottomFrame = CreateElement("Frame", {
        BackgroundColor3 = THEME_RED,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0.5, 0),
        Size = UDim2.new(1, 0, 0.5, 0),
        Parent = TitleBar
    })

    local TitleText = CreateElement("TextLabel", {
        Name = "TitleText",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 0),
        Size = UDim2.new(1, -120, 1, 0),
        Font = Enum.Font.GothamBold,
        Text = "Auto Game GUI",
        TextColor3 = THEME_WHITE,
        TextSize = 18,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = TitleBar
    })

    local ControlsFrame = CreateElement("Frame", {
        Name = "ControlsFrame",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -10, 0.5, 0),
        Size = UDim2.new(0, 90, 0, 24),
        Parent = TitleBar
    })

    local MinimizeButton = CreateElement("TextButton", {
        Name = "MinimizeButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -50, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "-",
        TextColor3 = THEME_WHITE,
        TextSize = 24,
        Parent = ControlsFrame
    })

    local ResizeButton = CreateElement("TextButton", {
        Name = "ResizeButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -25, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "□",
        TextColor3 = THEME_WHITE,
        TextSize = 18,
        Parent = ControlsFrame
    })

    local CloseButton = CreateElement("TextButton", {
        Name = "CloseButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, 0, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "×",
        TextColor3 = THEME_WHITE,
        TextSize = 24,
        Parent = ControlsFrame
    })

    local Dragging = false
    local DragInput
    local DragStart
    local StartPos

    local function UpdateDrag()
        if not DragInput then return end

        local Delta = DragInput.Position - DragStart
        local Position = UDim2.new(StartPos.X.Scale, StartPos.X.Offset + Delta.X, StartPos.Y.Scale, StartPos.Y.Offset + Delta.Y)

        TweenService:Create(MainFrame, TweenInfo.new(Settings.DragSpeed), {Position = Position}):Play()
    end

    TitleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Dragging = true
            DragStart = input.Position
            StartPos = MainFrame.Position

            input.Changed:Connect(function()
                if input.UserInputState == Enum.UserInputState.End then
                    Dragging = false
                    DragInput = nil
                end
            end)
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseMovement and Dragging then
            DragInput = input
            UpdateDrag()
        end
    end)

    CloseButton.MouseButton1Click:Connect(function()
        ScreenGui:Destroy()
        Settings.AutoRoll = false
        Settings.AutoSpin = false
        Settings.AutoRebirth = false
    end)

    MinimizeButton.MouseButton1Click:Connect(function()
        Settings.MinimizedState = not Settings.MinimizedState

        if Settings.MinimizedState then
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = UDim2.new(0, Settings.UISize.X.Offset, 0, 40)
            }):Play()
        else
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = Settings.UISize
            }):Play()
        end
    end)

    local IsMaximized = false
    local OriginalSize = Settings.UISize
    local OriginalPosition = UDim2.new(0.5, 0, 0.5, 0)

    ResizeButton.MouseButton1Click:Connect(function()
        IsMaximized = not IsMaximized

        if IsMaximized then
            OriginalSize = MainFrame.Size
            OriginalPosition = MainFrame.Position

            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = UDim2.new(0.8, 0, 0.8, 0),
                Position = UDim2.new(0.5, 0, 0.5, 0)
            }):Play()
        else
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = OriginalSize,
                Position = OriginalPosition
            }):Play()
        end
    end)

    local MainLayout = CreateElement("Frame", {
        Name = "MainLayout",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 40),
        Size = UDim2.new(1, 0, 1, -40),
        ClipsDescendants = true,
        Parent = MainFrame
    })

    local Sidebar = CreateElement("Frame", {
        Name = "Sidebar",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(0, 120, 1, 0),
        Parent = MainLayout
    })

    local SidebarCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = Sidebar
    })

    local SidebarFix = CreateElement("Frame", {
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 0, 0, 0),
        Size = UDim2.new(0.5, 0, 1, 0),
        Parent = Sidebar
    })

    local MenuContainer = CreateElement("Frame", {
        Name = "MenuContainer",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 10),
        Size = UDim2.new(1, 0, 1, -20),
        Parent = Sidebar
    })

    local MenuList = CreateElement("UIListLayout", {
        Padding = UDim.new(0, 8),
        HorizontalAlignment = Enum.HorizontalAlignment.Center,
        SortOrder = Enum.SortOrder.LayoutOrder,
        Parent = MenuContainer
    })

    local ContentArea = CreateElement("Frame", {
        Name = "ContentArea",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 120, 0, 0),
        Size = UDim2.new(1, -120, 1, 0),
        Parent = MainLayout
    })

    local MenuItems = {
        {Name = "Home", Icon = "🏠", Order = 1},
        {Name = "Settings", Icon = "⚙️", Order = 2}
    }

    local MenuButtons = {}
    local ContentFrames = {}

    for _, item in ipairs(MenuItems) do
        local MenuButton = CreateElement("TextButton", {
            Name = item.Name .. "Button",
            BackgroundColor3 = Settings.ActiveTab == item.Name and THEME_RED or THEME_GRAY_BLUE,
            BackgroundTransparency = Settings.ActiveTab == item.Name and 0.1 or 0.5,
            BorderSizePixel = 0,
            Size = UDim2.new(0.9, 0, 0, 36),
            Font = Enum.Font.GothamSemibold,
            Text = item.Icon .. " " .. item.Name,
            TextColor3 = THEME_WHITE,
            TextSize = 14,
            LayoutOrder = item.Order,
            Parent = MenuContainer
        })

        local ButtonCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = MenuButton
        })

        local ContentFrame = CreateElement("ScrollingFrame", {
            Name = item.Name .. "Content",
            BackgroundTransparency = 1,
            BorderSizePixel = 0,
            Position = UDim2.new(0, 15, 0, 15),
            Size = UDim2.new(1, -30, 1, -30),
            CanvasSize = UDim2.new(0, 0, 0, 0),
            ScrollBarThickness = 4,
            ScrollBarImageColor3 = THEME_RED,
            Visible = Settings.ActiveTab == item.Name,
            Parent = ContentArea
        })

        MenuButtons[item.Name] = MenuButton
        ContentFrames[item.Name] = ContentFrame

        MenuButton.MouseButton1Click:Connect(function()
            Settings.ActiveTab = item.Name

            for name, button in pairs(MenuButtons) do
                TweenService:Create(button, TweenInfo.new(0.2), {
                    BackgroundColor3 = name == item.Name and THEME_RED or THEME_GRAY_BLUE,
                    BackgroundTransparency = name == item.Name and 0.1 or 0.5
                }):Play()
            end

            for name, frame in pairs(ContentFrames) do
                frame.Visible = name == item.Name
            end
        end)
    end

    local HomeContent = ContentFrames["Home"]

    -- Auto Roll Card
    local RollCard = CreateElement("Frame", {
        Name = "RollCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local RollCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = RollCard
    })

    local RollTitle = CreateElement("TextLabel", {
        Name = "RollTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Roll Dice",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RollCard
    })

    local RollStatus = CreateElement("TextLabel", {
        Name = "RollStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RollCard
    })

    local RollToggle = CreateElement("Frame", {
        Name = "RollToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = RollCard
    })

    local RollToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = RollToggle
    })

    local RollToggleCircle = CreateElement("Frame", {
        Name = "RollToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = RollToggle
    })

    local RollCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = RollToggleCircle
    })

    -- Auto Spin Card
    local SpinCard = CreateElement("Frame", {
        Name = "SpinCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 90),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local SpinCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = SpinCard
    })

    local SpinTitle = CreateElement("TextLabel", {
        Name = "SpinTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Spin",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = SpinCard
    })

    local SpinStatus = CreateElement("TextLabel", {
        Name = "SpinStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = SpinCard
    })

    local SpinToggle = CreateElement("Frame", {
        Name = "SpinToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = SpinCard
    })

    local SpinToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = SpinToggle
    })

    local SpinToggleCircle = CreateElement("Frame", {
        Name = "SpinToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = SpinToggle
    })

    local SpinCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = SpinToggleCircle
    })

    -- Auto Rebirth Card
    local RebirthCard = CreateElement("Frame", {
        Name = "RebirthCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 180),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    -- Auto Collect Coins Card
    local CoinsCard = CreateElement("Frame", {
        Name = "CoinsCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 270),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local CoinsCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = CoinsCard
    })

    local CoinsTitle = CreateElement("TextLabel", {
        Name = "CoinsTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Collect Coins",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = CoinsCard
    })

    local CoinsStatus = CreateElement("TextLabel", {
        Name = "CoinsStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = CoinsCard
    })

    local CoinsToggle = CreateElement("Frame", {
        Name = "CoinsToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = CoinsCard
    })

    local CoinsToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = CoinsToggle
    })

    local CoinsToggleCircle = CreateElement("Frame", {
        Name = "CoinsToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = CoinsToggle
    })

    local CoinsCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = CoinsToggleCircle
    })

    -- Auto Collect Gems Card
    local GemsCard = CreateElement("Frame", {
        Name = "GemsCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 360),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local GemsCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = GemsCard
    })

    local GemsTitle = CreateElement("TextLabel", {
        Name = "GemsTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Collect Gems",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = GemsCard
    })

    local GemsStatus = CreateElement("TextLabel", {
        Name = "GemsStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = GemsCard
    })

    local GemsToggle = CreateElement("Frame", {
        Name = "GemsToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = GemsCard
    })

    local GemsToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = GemsToggle
    })

    local GemsToggleCircle = CreateElement("Frame", {
        Name = "GemsToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = GemsToggle
    })

    local GemsCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = GemsToggleCircle
    })

    -- Auto Collect Direct Coins Card
    local DirectCoinsCard = CreateElement("Frame", {
        Name = "DirectCoinsCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 450),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local DirectCoinsCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = DirectCoinsCard
    })

    local DirectCoinsTitle = CreateElement("TextLabel", {
        Name = "DirectCoinsTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Collect Direct Coins",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = DirectCoinsCard
    })

    local DirectCoinsStatus = CreateElement("TextLabel", {
        Name = "DirectCoinsStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = DirectCoinsCard
    })

    local DirectCoinsToggle = CreateElement("Frame", {
        Name = "DirectCoinsToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = DirectCoinsCard
    })

    local DirectCoinsToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = DirectCoinsToggle
    })

    local DirectCoinsToggleCircle = CreateElement("Frame", {
        Name = "DirectCoinsToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = DirectCoinsToggle
    })

    local DirectCoinsCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = DirectCoinsToggleCircle
    })

    -- Auto Collect XPorbs Card
    local XPorbsCard = CreateElement("Frame", {
        Name = "XPorbsCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 540),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local XPorbsCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = XPorbsCard
    })

    local XPorbsTitle = CreateElement("TextLabel", {
        Name = "XPorbsTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Collect XPorbs",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = XPorbsCard
    })

    local XPorbsStatus = CreateElement("TextLabel", {
        Name = "XPorbsStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = XPorbsCard
    })

    local XPorbsToggle = CreateElement("Frame", {
        Name = "XPorbsToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = XPorbsCard
    })

    local XPorbsToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = XPorbsToggle
    })

    local XPorbsToggleCircle = CreateElement("Frame", {
        Name = "XPorbsToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = XPorbsToggle
    })

    local XPorbsCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = XPorbsToggleCircle
    })

    local RebirthCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = RebirthCard
    })

    local RebirthTitle = CreateElement("TextLabel", {
        Name = "RebirthTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Rebirth",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RebirthCard
    })

    local RebirthStatus = CreateElement("TextLabel", {
        Name = "RebirthStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RebirthCard
    })

    local RebirthToggle = CreateElement("Frame", {
        Name = "RebirthToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 50, 0, 24),
        Parent = RebirthCard
    })

    local RebirthToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12),
        Parent = RebirthToggle
    })

    local RebirthToggleCircle = CreateElement("Frame", {
        Name = "RebirthToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = RebirthToggle
    })

    local RebirthCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = RebirthToggleCircle
    })

    return {
        ScreenGui = ScreenGui,
        ContentFrames = ContentFrames,
        RollStatus = RollStatus,
        SpinStatus = SpinStatus,
        RebirthStatus = RebirthStatus,
        CoinsStatus = CoinsStatus,
        GemsStatus = GemsStatus,
        DirectCoinsStatus = DirectCoinsStatus,
        XPorbsStatus = XPorbsStatus,
        RollToggle = RollToggle,
        SpinToggle = SpinToggle,
        RebirthToggle = RebirthToggle,
        CoinsToggle = CoinsToggle,
        GemsToggle = GemsToggle,
        DirectCoinsToggle = DirectCoinsToggle,
        XPorbsToggle = XPorbsToggle,
        RollToggleCircle = RollToggleCircle,
        SpinToggleCircle = SpinToggleCircle,
        RebirthToggleCircle = RebirthToggleCircle,
        CoinsToggleCircle = CoinsToggleCircle,
        GemsToggleCircle = GemsToggleCircle,
        DirectCoinsToggleCircle = DirectCoinsToggleCircle,
        XPorbsToggleCircle = XPorbsToggleCircle
    }
end

local function StartAutoFarm()
    local GUI = CreateGUI()
    local SettingsContent = GUI.ContentFrames["Settings"]

    -- Create Settings Tab Content
    -- Roll Interval Setting
    local RollIntervalCard = CreateElement("Frame", {
        Name = "RollIntervalCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 80),
        Parent = SettingsContent
    })

    local RollIntervalCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = RollIntervalCard
    })

    local RollIntervalTitle = CreateElement("TextLabel", {
        Name = "RollIntervalTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Roll Interval",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RollIntervalCard
    })

    local RollIntervalLabel = CreateElement("TextLabel", {
        Name = "RollIntervalLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 35),
        Size = UDim2.new(0, 100, 0, 20),
        Font = Enum.Font.Gotham,
        Text = Settings.RollInterval .. " seconds",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RollIntervalCard
    })

    local RollIntervalSlider = CreateElement("Frame", {
        Name = "RollIntervalSlider",
        BackgroundColor3 = THEME_LIGHT_GRAY,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 120, 0, 45),
        Size = UDim2.new(0.7, -130, 0, 6),
        Parent = RollIntervalCard
    })

    local RollSliderCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = RollIntervalSlider
    })

    local RollSliderFill = CreateElement("Frame", {
        Name = "RollSliderFill",
        BackgroundColor3 = THEME_RED,
        BorderSizePixel = 0,
        Size = UDim2.new((Settings.RollInterval - 0.001) / 0.099, 0, 1, 0), -- Range 0.001-0.1 seconds
        Parent = RollIntervalSlider
    })

    local RollFillCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = RollSliderFill
    })

    local RollSliderButton = CreateElement("TextButton", {
        Name = "RollSliderButton",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new((Settings.RollInterval - 0.001) / 0.099, 0, 0.5, 0), -- Range 0.001-0.1 seconds
        Size = UDim2.new(0, 16, 0, 16),
        Text = "",
        Parent = RollIntervalSlider
    })

    local RollButtonCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = RollSliderButton
    })

    -- Spin Interval Setting
    local SpinIntervalCard = CreateElement("Frame", {
        Name = "SpinIntervalCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 90),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = SettingsContent
    })

    local SpinIntervalCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = SpinIntervalCard
    })

    local SpinIntervalTitle = CreateElement("TextLabel", {
        Name = "SpinIntervalTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Spin Interval",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = SpinIntervalCard
    })

    local SpinIntervalLabel = CreateElement("TextLabel", {
        Name = "SpinIntervalLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 35),
        Size = UDim2.new(0, 100, 0, 20),
        Font = Enum.Font.Gotham,
        Text = Settings.SpinInterval .. " seconds",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = SpinIntervalCard
    })

    local SpinIntervalSlider = CreateElement("Frame", {
        Name = "SpinIntervalSlider",
        BackgroundColor3 = THEME_LIGHT_GRAY,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 120, 0, 45),
        Size = UDim2.new(0.7, -130, 0, 6),
        Parent = SpinIntervalCard
    })

    local SpinSliderCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = SpinIntervalSlider
    })

    local SpinSliderFill = CreateElement("Frame", {
        Name = "SpinSliderFill",
        BackgroundColor3 = THEME_RED,
        BorderSizePixel = 0,
        Size = UDim2.new((Settings.SpinInterval - 0.001) / 0.099, 0, 1, 0), -- Range 0.001-0.1 seconds
        Parent = SpinIntervalSlider
    })

    local SpinFillCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = SpinSliderFill
    })

    local SpinSliderButton = CreateElement("TextButton", {
        Name = "SpinSliderButton",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new((Settings.SpinInterval - 0.001) / 0.099, 0, 0.5, 0), -- Range 0.001-0.1 seconds
        Size = UDim2.new(0, 16, 0, 16),
        Text = "",
        Parent = SpinIntervalSlider
    })

    local SpinButtonCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = SpinSliderButton
    })

    -- Rebirth Interval Setting
    local RebirthIntervalCard = CreateElement("Frame", {
        Name = "RebirthIntervalCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 180),
        Size = UDim2.new(1, 0, 0, 80),
        Parent = SettingsContent
    })

    local RebirthIntervalCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = RebirthIntervalCard
    })

    local RebirthIntervalTitle = CreateElement("TextLabel", {
        Name = "RebirthIntervalTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Rebirth Interval",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RebirthIntervalCard
    })

    local RebirthIntervalLabel = CreateElement("TextLabel", {
        Name = "RebirthIntervalLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 35),
        Size = UDim2.new(0, 100, 0, 20),
        Font = Enum.Font.Gotham,
        Text = Settings.RebirthInterval .. " seconds",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = RebirthIntervalCard
    })

    local RebirthIntervalSlider = CreateElement("Frame", {
        Name = "RebirthIntervalSlider",
        BackgroundColor3 = THEME_LIGHT_GRAY,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 120, 0, 45),
        Size = UDim2.new(0.7, -130, 0, 6),
        Parent = RebirthIntervalCard
    })

    local RebirthSliderCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = RebirthIntervalSlider
    })

    local RebirthSliderFill = CreateElement("Frame", {
        Name = "RebirthSliderFill",
        BackgroundColor3 = THEME_RED,
        BorderSizePixel = 0,
        Size = UDim2.new((Settings.RebirthInterval - 0.001) / 0.099, 0, 1, 0), -- Range 0.001-0.1 seconds
        Parent = RebirthIntervalSlider
    })

    local RebirthFillCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 3),
        Parent = RebirthSliderFill
    })

    local RebirthSliderButton = CreateElement("TextButton", {
        Name = "RebirthSliderButton",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new((Settings.RebirthInterval - 0.001) / 0.099, 0, 0.5, 0), -- Range 0.001-0.1 seconds
        Size = UDim2.new(0, 16, 0, 16),
        Text = "",
        Parent = RebirthIntervalSlider
    })

    local RebirthButtonCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = RebirthSliderButton
    })

    -- Spin Reward Selection
    local SpinRewardCard = CreateElement("Frame", {
        Name = "SpinRewardCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 270),
        Size = UDim2.new(1, 0, 0, 160), -- Increased height to accommodate more rewards
        Parent = SettingsContent
    })

    local SpinRewardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = SpinRewardCard
    })

    local SpinRewardTitle = CreateElement("TextLabel", {
        Name = "SpinRewardTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 10),
        Size = UDim2.new(1, -30, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Spin Reward Selection",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = SpinRewardCard
    })

    local SpinRewardContainer = CreateElement("Frame", {
        Name = "SpinRewardContainer",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 15, 0, 40),
        Size = UDim2.new(1, -30, 0, 110), -- Increased height for more rows
        Parent = SpinRewardCard
    })

    local SpinRewardLayout = CreateElement("UIGridLayout", {
        CellPadding = UDim2.new(0, 8, 0, 8), -- Reduced padding to fit more buttons
        CellSize = UDim2.new(0, 75, 0, 30), -- Slightly smaller buttons
        HorizontalAlignment = Enum.HorizontalAlignment.Left,
        SortOrder = Enum.SortOrder.LayoutOrder,
        Parent = SpinRewardContainer
    })

    local rewardOptions = {"Reward1", "Reward2", "Reward3", "Reward4", "Reward5", "Reward6", "Reward7"}
    local rewardButtons = {}

    for i, reward in ipairs(rewardOptions) do
        local RewardButton = CreateElement("TextButton", {
            Name = reward .. "Button",
            BackgroundColor3 = Settings.SelectedReward == reward and THEME_RED or THEME_GRAY_BLUE,
            BackgroundTransparency = 0.5,
            BorderSizePixel = 0,
            Font = Enum.Font.Gotham,
            Text = reward,
            TextColor3 = THEME_WHITE,
            TextSize = 14,
            LayoutOrder = i,
            Parent = SpinRewardContainer
        })

        local RewardButtonCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = RewardButton
        })

        rewardButtons[reward] = RewardButton

        RewardButton.MouseButton1Click:Connect(function()
            Settings.SelectedReward = reward

            for name, button in pairs(rewardButtons) do
                TweenService:Create(button, TweenInfo.new(0.2), {
                    BackgroundColor3 = name == reward and THEME_RED or THEME_GRAY_BLUE
                }):Play()
            end
        end)
    end

    -- Slider functionality
    local function UpdateRollSlider(input)
        local SliderPosition = math.clamp((input.Position.X - RollIntervalSlider.AbsolutePosition.X) / RollIntervalSlider.AbsoluteSize.X, 0, 1)
        Settings.RollInterval = SliderPosition * 0.099 + 0.001 -- Range 0.001-0.1 seconds

        RollSliderFill.Size = UDim2.new(SliderPosition, 0, 1, 0)
        RollSliderButton.Position = UDim2.new(SliderPosition, 0, 0.5, 0)
        RollIntervalLabel.Text = string.format("%.3f seconds", Settings.RollInterval)
    end

    local function UpdateSpinSlider(input)
        local SliderPosition = math.clamp((input.Position.X - SpinIntervalSlider.AbsolutePosition.X) / SpinIntervalSlider.AbsoluteSize.X, 0, 1)
        Settings.SpinInterval = SliderPosition * 0.099 + 0.001 -- Range 0.001-0.1 seconds

        SpinSliderFill.Size = UDim2.new(SliderPosition, 0, 1, 0)
        SpinSliderButton.Position = UDim2.new(SliderPosition, 0, 0.5, 0)
        SpinIntervalLabel.Text = string.format("%.3f seconds", Settings.SpinInterval)
    end

    local function UpdateRebirthSlider(input)
        local SliderPosition = math.clamp((input.Position.X - RebirthIntervalSlider.AbsolutePosition.X) / RebirthIntervalSlider.AbsoluteSize.X, 0, 1)
        Settings.RebirthInterval = SliderPosition * 0.099 + 0.001 -- Range 0.001-0.1 seconds

        RebirthSliderFill.Size = UDim2.new(SliderPosition, 0, 1, 0)
        RebirthSliderButton.Position = UDim2.new(SliderPosition, 0, 0.5, 0)
        RebirthIntervalLabel.Text = string.format("%.3f seconds", Settings.RebirthInterval)
    end

    -- Roll slider events
    RollSliderButton.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateRollSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    RollIntervalSlider.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            UpdateRollSlider(input)

            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateRollSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    -- Spin slider events
    SpinSliderButton.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateSpinSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    SpinIntervalSlider.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            UpdateSpinSlider(input)

            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateSpinSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    -- Rebirth slider events
    RebirthSliderButton.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateRebirthSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    RebirthIntervalSlider.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            UpdateRebirthSlider(input)

            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) then
                    UpdateRebirthSlider(input)
                else
                    Connection:Disconnect()
                end
            end)
        end
    end)

    -- Toggle handlers
    local function UpdateRollToggle()
        local TogglePos = Settings.AutoRoll and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoRoll and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(GUI.RollToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(GUI.RollToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        GUI.RollStatus.Text = "Status: " .. (Settings.AutoRoll and "Running" or "Idle")
    end

    local function UpdateSpinToggle()
        local TogglePos = Settings.AutoSpin and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoSpin and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(GUI.SpinToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(GUI.SpinToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        GUI.SpinStatus.Text = "Status: " .. (Settings.AutoSpin and "Running" or "Idle")
    end

    local function UpdateRebirthToggle()
        local TogglePos = Settings.AutoRebirth and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoRebirth and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(GUI.RebirthToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(GUI.RebirthToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        GUI.RebirthStatus.Text = "Status: " .. (Settings.AutoRebirth and "Running" or "Idle")
    end

    local function UpdateCoinsToggle()
        local TogglePos = Settings.AutoCollectCoins and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoCollectCoins and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(GUI.CoinsToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(GUI.CoinsToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        GUI.CoinsStatus.Text = "Status: " .. (Settings.AutoCollectCoins and "Running" or "Idle")
    end

    local function UpdateGemsToggle()
        local TogglePos = Settings.AutoCollectGems and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoCollectGems and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(GUI.GemsToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(GUI.GemsToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        GUI.GemsStatus.Text = "Status: " .. (Settings.AutoCollectGems and "Running" or "Idle")
    end

    local function UpdateDirectCoinsToggle()
        local TogglePos = Settings.AutoCollectDirectCoins and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoCollectDirectCoins and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(GUI.DirectCoinsToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(GUI.DirectCoinsToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        GUI.DirectCoinsStatus.Text = "Status: " .. (Settings.AutoCollectDirectCoins and "Running" or "Idle")
    end

    local function UpdateXPorbsToggle()
        local TogglePos = Settings.AutoCollectXPorbs and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoCollectXPorbs and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(GUI.XPorbsToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(GUI.XPorbsToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        GUI.XPorbsStatus.Text = "Status: " .. (Settings.AutoCollectXPorbs and "Running" or "Idle")
    end

    -- Connect toggle buttons
    GUI.RollToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoRoll = not Settings.AutoRoll
            UpdateRollToggle()
        end
    end)

    GUI.SpinToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoSpin = not Settings.AutoSpin
            UpdateSpinToggle()
        end
    end)

    GUI.RebirthToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoRebirth = not Settings.AutoRebirth
            UpdateRebirthToggle()
        end
    end)

    GUI.CoinsToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoCollectCoins = not Settings.AutoCollectCoins
            UpdateCoinsToggle()
        end
    end)

    GUI.GemsToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoCollectGems = not Settings.AutoCollectGems
            UpdateGemsToggle()
        end
    end)

    GUI.DirectCoinsToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoCollectDirectCoins = not Settings.AutoCollectDirectCoins
            UpdateDirectCoinsToggle()
        end
    end)

    GUI.XPorbsToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoCollectXPorbs = not Settings.AutoCollectXPorbs
            UpdateXPorbsToggle()
        end
    end)

    -- Auto farm loops
    local LastRollTime = 0
    local LastSpinTime = 0
    local LastRebirthTime = 0
    local LastCollectTime = 0

    -- Ultra-fast Auto Roll loop (separate from main loop)
    spawn(function()
        local Roll = ReplicatedStorage.Remotes.Roll

        while true do
            if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
                break
            end

            if Settings.AutoRoll then
                -- Fire roll event as fast as possible with minimal delay
                Roll:FireServer()

                -- Tiny wait to prevent client freeze and allow other processes to run
                wait(Settings.RollInterval) -- Ultra-fast interval (0.005 seconds)
            else
                -- When not active, just check occasionally
                wait(0.005) -- Still check frequently
            end
        end
    end)

    -- Ultra-fast Auto Spin loop (separate from main loop)
    spawn(function()
        local Spin = ReplicatedStorage.Events.Spin

        while true do
            if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
                break
            end

            if Settings.AutoSpin then
                -- Print current reward for debugging
                print("Auto Spinning with reward: " .. Settings.SelectedReward)

                -- Fire spin event as fast as possible with minimal delay
                Spin:FireServer(Settings.SelectedReward)

                -- Tiny wait to prevent client freeze and allow other processes to run
                wait(Settings.SpinInterval) -- Ultra-fast interval (0.005 seconds)
            else
                -- When not active, just check occasionally
                wait(0.005) -- Still check frequently
            end
        end
    end)

    -- Ultra-fast Auto Rebirth loop (separate from main loop)
    spawn(function()
        local Rebirths = workspace.Events.Rebirths

        while true do
            if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
                break
            end

            if Settings.AutoRebirth then
                -- Fire rebirth event as fast as possible with minimal delay
                Rebirths:FireServer()

                -- Tiny wait to prevent client freeze and allow other processes to run
                wait(Settings.RebirthInterval) -- Ultra-fast interval (0.005 seconds)
            else
                -- When not active, just check occasionally
                wait(0.005) -- Still check frequently
            end
        end
    end)

    -- Function to collect items using TouchInterest
    local function CollectItems(itemType)
        local folders = {}
        local statusLabel
        local character = Player.Character or Player.CharacterAdded:Wait()
        local humanoidRootPart = character:WaitForChild("HumanoidRootPart")

        if itemType == "Coins" then
            folders = {"Coins1", "Coins2", "Coins3", "Coins4", "Coins5", "Coins6", "Coins7", "Coins8", "Coins9", "Coins10"}
            statusLabel = GUI.CoinsStatus
        elseif itemType == "Gems" then
            folders = {"Gems1", "Gems2", "Gems3", "Gems4", "Gems5"}
            statusLabel = GUI.GemsStatus
        elseif itemType == "DirectCoins" then
            statusLabel = GUI.DirectCoinsStatus
        elseif itemType == "XPorbs" then
            statusLabel = GUI.XPorbsStatus
        end

        statusLabel.Text = "Status: Collecting " .. itemType .. "..."

        -- Recursive function to find and touch all items with TouchInterest
        local function touchAllItems(parent)
            for _, item in ipairs(parent:GetChildren()) do
                if item:FindFirstChild("TouchInterest") then
                    -- Found an item with TouchInterest, touch it
                    firetouchinterest(humanoidRootPart, item, 0) -- Touch begin
                    firetouchinterest(humanoidRootPart, item, 1) -- Touch end
                end

                -- Check if this is a container with more items inside
                if #item:GetChildren() > 0 then
                    touchAllItems(item) -- Recursively check children
                end
            end
        end

        -- Handle different collection types
        if itemType == "DirectCoins" then
            -- Direct Coins in workspace
            -- Cari semua Coin yang ada langsung di workspace
            print("Mencari direct coins di workspace...")

            -- Metode 1: Cari semua instance dengan nama "Coin" di workspace
            for _, item in ipairs(workspace:GetChildren()) do
                if item.Name == "Coin" then
                    print("Menemukan Coin: " .. item:GetFullName())

                    -- Cek apakah item ini memiliki TouchInterest
                    if item:FindFirstChild("TouchInterest") then
                        print("Mengambil coin dengan TouchInterest: " .. item:GetFullName())
                        firetouchinterest(humanoidRootPart, item, 0) -- Touch begin
                        firetouchinterest(humanoidRootPart, item, 1) -- Touch end
                    end
                end
            end

            -- Metode 2: Coba cari dengan GetDescendants untuk menangkap semua Coin di workspace
            for _, item in ipairs(workspace:GetDescendants()) do
                if item.Name == "Coin" and item:IsA("BasePart") and item:FindFirstChild("TouchInterest") then
                    print("Mengambil coin (metode 2): " .. item:GetFullName())
                    firetouchinterest(humanoidRootPart, item, 0) -- Touch begin
                    firetouchinterest(humanoidRootPart, item, 1) -- Touch end
                end
            end
        elseif itemType == "XPorbs" then
            -- XPorbs in IslandXP folder
            print("Mencari XPorbs di IslandXP folder...")

            -- Metode 1: Cari di folder IslandXP
            local islandXP = workspace:FindFirstChild("IslandXP")
            if islandXP then
                print("Folder IslandXP ditemukan")
                for _, orb in ipairs(islandXP:GetChildren()) do
                    if orb.Name == "XPorb" then
                        print("Menemukan XPorb: " .. orb:GetFullName())
                        if orb:FindFirstChild("TouchInterest") then
                            print("Mengambil XPorb dengan TouchInterest: " .. orb:GetFullName())
                            firetouchinterest(humanoidRootPart, orb, 0) -- Touch begin
                            firetouchinterest(humanoidRootPart, orb, 1) -- Touch end
                        end
                    end
                end

                -- Cari lebih dalam dengan GetDescendants
                for _, orb in ipairs(islandXP:GetDescendants()) do
                    if orb.Name == "XPorb" and orb:FindFirstChild("TouchInterest") then
                        print("Mengambil XPorb (metode dalam): " .. orb:GetFullName())
                        firetouchinterest(humanoidRootPart, orb, 0) -- Touch begin
                        firetouchinterest(humanoidRootPart, orb, 1) -- Touch end
                    end
                end
            else
                print("Folder IslandXP tidak ditemukan, mencari di seluruh workspace...")

                -- Metode 2: Cari di seluruh workspace jika folder IslandXP tidak ditemukan
                for _, item in ipairs(workspace:GetDescendants()) do
                    if item.Name == "XPorb" and item:FindFirstChild("TouchInterest") then
                        print("Mengambil XPorb (metode global): " .. item:GetFullName())
                        firetouchinterest(humanoidRootPart, item, 0) -- Touch begin
                        firetouchinterest(humanoidRootPart, item, 1) -- Touch end
                    end
                end
            end
        else
            -- Process each folder for Coins and Gems
            for _, folderName in ipairs(folders) do
                local folder = workspace:FindFirstChild(folderName)
                if folder then
                    touchAllItems(folder) -- Process the entire folder recursively
                end
            end
        end

        -- Update status
        if (itemType == "Coins" and Settings.AutoCollectCoins) or
           (itemType == "Gems" and Settings.AutoCollectGems) or
           (itemType == "DirectCoins" and Settings.AutoCollectDirectCoins) or
           (itemType == "XPorbs" and Settings.AutoCollectXPorbs) then
            statusLabel.Text = "Status: Running"
        else
            statusLabel.Text = "Status: Idle"
        end
    end

    local LastCollectTime = 0

    -- Ultra-fast Auto Collect loop (separate from main loop)
    spawn(function()
        while true do
            if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
                break
            end

            if Settings.AutoCollectCoins then
                spawn(function()
                    -- Use pcall to catch any errors that might occur
                    local success, errorMsg = pcall(function()
                        CollectItems("Coins")
                    end)

                    if not success then
                        -- If there was an error, print it to the output
                        warn("Auto Collect Coins Error: " .. tostring(errorMsg))
                        -- Update status to show error
                        GUI.CoinsStatus.Text = "Status: Error"
                        -- Wait a bit before trying again
                        wait(1)
                    end
                end)
            end

            if Settings.AutoCollectGems then
                spawn(function()
                    -- Use pcall to catch any errors that might occur
                    local success, errorMsg = pcall(function()
                        CollectItems("Gems")
                    end)

                    if not success then
                        -- If there was an error, print it to the output
                        warn("Auto Collect Gems Error: " .. tostring(errorMsg))
                        -- Update status to show error
                        GUI.GemsStatus.Text = "Status: Error"
                        -- Wait a bit before trying again
                        wait(1)
                    end
                end)
            end

            if Settings.AutoCollectDirectCoins then
                spawn(function()
                    -- Use pcall to catch any errors that might occur
                    local success, errorMsg = pcall(function()
                        CollectItems("DirectCoins")
                    end)

                    if not success then
                        -- If there was an error, print it to the output
                        warn("Auto Collect Direct Coins Error: " .. tostring(errorMsg))
                        -- Update status to show error
                        GUI.DirectCoinsStatus.Text = "Status: Error"
                        -- Wait a bit before trying again
                        wait(1)
                    end
                end)
            end

            if Settings.AutoCollectXPorbs then
                spawn(function()
                    -- Use pcall to catch any errors that might occur
                    local success, errorMsg = pcall(function()
                        CollectItems("XPorbs")
                    end)

                    if not success then
                        -- If there was an error, print it to the output
                        warn("Auto Collect XPorbs Error: " .. tostring(errorMsg))
                        -- Update status to show error
                        GUI.XPorbsStatus.Text = "Status: Error"
                        -- Wait a bit before trying again
                        wait(1)
                    end
                end)
            end

            -- Wait using the collect interval setting
            if Settings.AutoCollectCoins or Settings.AutoCollectGems or
               Settings.AutoCollectDirectCoins or Settings.AutoCollectXPorbs then
                wait(Settings.CollectInterval) -- Ultra-fast interval (0.005 seconds)
            else
                wait(0.005) -- Still check frequently
            end
        end
    end)

    -- Main loop for status updates only
    spawn(function()
        while wait(0.005) do -- Use faster loop interval
            if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
                break
            end

            -- Status updates only - actual functionality is in separate loops
            if Settings.AutoRoll then
                GUI.RollStatus.Text = "Status: Rolling..."
            else
                GUI.RollStatus.Text = "Status: Idle"
            end

            if Settings.AutoSpin then
                GUI.SpinStatus.Text = "Status: Spinning... (" .. Settings.SelectedReward .. ")"
            else
                GUI.SpinStatus.Text = "Status: Idle"
            end

            if Settings.AutoRebirth then
                GUI.RebirthStatus.Text = "Status: Rebirthing..."
            else
                GUI.RebirthStatus.Text = "Status: Idle"
            end

            if Settings.AutoCollectCoins then
                GUI.CoinsStatus.Text = "Status: Collecting Coins..."
            else
                GUI.CoinsStatus.Text = "Status: Idle"
            end

            if Settings.AutoCollectGems then
                GUI.GemsStatus.Text = "Status: Collecting Gems..."
            else
                GUI.GemsStatus.Text = "Status: Idle"
            end

            if Settings.AutoCollectDirectCoins then
                GUI.DirectCoinsStatus.Text = "Status: Collecting Direct Coins..."
            else
                GUI.DirectCoinsStatus.Text = "Status: Idle"
            end

            if Settings.AutoCollectXPorbs then
                GUI.XPorbsStatus.Text = "Status: Collecting XPorbs..."
            else
                GUI.XPorbsStatus.Text = "Status: Idle"
            end
        end
    end)

    return GUI
end

-- Start the auto farm
StartAutoFarm()