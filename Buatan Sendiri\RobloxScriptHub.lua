--[[
    Roblox Script Hub - Modular Edition
    Base GUI inspired by AutoClickUGC2.lua
    Created with modular architecture for easy feature expansion
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")

-- Enhanced Theme System with Multiple Themes
local THEMES = {
    Netflix = {
        PRIMARY = Color3.fromRGB(229, 9, 20),
        BACKGROUND = Color3.fromRGB(20, 20, 20),
        SURFACE = Color3.fromRGB(35, 35, 35),
        SURFACE_VARIANT = Color3.fromRGB(70, 70, 70),
        ON_SURFACE = Color3.fromRGB(255, 255, 255),
        SECONDARY = Color3.fromRGB(30, 35, 45),
        SUCCESS = Color3.fromRGB(46, 125, 50),
        WARNING = Color3.fromRGB(255, 152, 0),
        ERROR = Color3.fromRGB(244, 67, 54),
        INFO = Color3.fromRGB(33, 150, 243)
    },

    Discord = {
        PRIMARY = Color3.fromRGB(88, 101, 242),
        BACKGROUND = Color3.fromRGB(54, 57, 63),
        SURFACE = Color3.fromRGB(47, 49, 54),
        SURFACE_VARIANT = Color3.fromRGB(64, 68, 75),
        ON_SURFACE = Color3.fromRGB(255, 255, 255),
        SECONDARY = Color3.fromRGB(32, 34, 37),
        SUCCESS = Color3.fromRGB(87, 242, 135),
        WARNING = Color3.fromRGB(254, 231, 92),
        ERROR = Color3.fromRGB(237, 66, 69),
        INFO = Color3.fromRGB(114, 137, 218)
    },

    Ocean = {
        PRIMARY = Color3.fromRGB(0, 150, 136),
        BACKGROUND = Color3.fromRGB(18, 32, 47),
        SURFACE = Color3.fromRGB(25, 42, 61),
        SURFACE_VARIANT = Color3.fromRGB(55, 71, 79),
        ON_SURFACE = Color3.fromRGB(255, 255, 255),
        SECONDARY = Color3.fromRGB(69, 90, 100),
        SUCCESS = Color3.fromRGB(76, 175, 80),
        WARNING = Color3.fromRGB(255, 193, 7),
        ERROR = Color3.fromRGB(244, 67, 54),
        INFO = Color3.fromRGB(33, 150, 243)
    }
}

-- Current theme reference (defaults to Netflix)
local THEME = THEMES.Netflix

-- Theme manager
local ThemeManager = {
    CurrentTheme = "Netflix",
    ThemeElements = {}
}

function ThemeManager:SetTheme(themeName)
    if THEMES[themeName] then
        self.CurrentTheme = themeName
        THEME = THEMES[themeName]
        self:UpdateAllElements()
        Settings.CurrentTheme = themeName
    end
end

function ThemeManager:RegisterElement(element, colorProperty, themeColor)
    if not self.ThemeElements[element] then
        self.ThemeElements[element] = {}
    end
    self.ThemeElements[element][colorProperty] = themeColor
end

function ThemeManager:UpdateAllElements()
    for element, properties in pairs(self.ThemeElements) do
        if element and element.Parent then
            for property, themeColor in pairs(properties) do
                if THEME[themeColor] then
                    element[property] = THEME[themeColor]
                end
            end
        end
    end
end

-- Enhanced Configuration System with persistence
local ConfigManager = {}
ConfigManager.ConfigFile = "RobloxScriptHub_Config.json"

-- Default Settings
local DefaultSettings = {
    Transparency = 0.2,
    DragSpeed = 0.05,
    MinimizedState = false,
    ActiveTab = "Home",
    UISize = UDim2.new(0, 600, 0, 400),
    CurrentTheme = "Netflix",
    AnimationSpeed = 0.3,
    EnableAnimations = true,
    EnableSounds = false,
    AutoSave = true,
    ModuleSettings = {},
    WindowPosition = UDim2.new(0.5, 0, 0.5, 0),
    LastUsed = os.time()
}

-- Global Settings (loaded from config or defaults)
local Settings = {}

-- Configuration Management Functions
function ConfigManager:LoadConfig()
    -- In a real implementation, this would load from a file
    -- For now, we'll use the defaults and add some basic persistence via _G
    if _G.RobloxScriptHubConfig then
        for key, value in pairs(_G.RobloxScriptHubConfig) do
            if DefaultSettings[key] ~= nil then
                Settings[key] = value
            end
        end
    else
        Settings = DefaultSettings
    end

    -- Ensure all default keys exist
    for key, value in pairs(DefaultSettings) do
        if Settings[key] == nil then
            Settings[key] = value
        end
    end

    -- Apply loaded theme
    if Settings.CurrentTheme and THEMES[Settings.CurrentTheme] then
        ThemeManager:SetTheme(Settings.CurrentTheme)
    end
end

function ConfigManager:SaveConfig()
    if Settings.AutoSave then
        Settings.LastUsed = os.time()
        _G.RobloxScriptHubConfig = Settings
        print("📁 Configuration saved successfully")
    end
end

function ConfigManager:ResetConfig()
    Settings = DefaultSettings
    _G.RobloxScriptHubConfig = nil
    print("🔄 Configuration reset to defaults")
end

function ConfigManager:ExportConfig()
    local configString = "-- Roblox Script Hub Configuration Export\n"
    configString = configString .. "-- Generated on: " .. os.date() .. "\n\n"
    configString = configString .. "return {\n"

    for key, value in pairs(Settings) do
        if type(value) == "string" then
            configString = configString .. "    " .. key .. ' = "' .. value .. '",\n'
        elseif type(value) == "number" or type(value) == "boolean" then
            configString = configString .. "    " .. key .. " = " .. tostring(value) .. ",\n"
        end
    end

    configString = configString .. "}"
    return configString
end

-- Initialize configuration
ConfigManager:LoadConfig()

-- Animation utilities for smooth UI transitions
local AnimationUtils = {}

function AnimationUtils:Tween(object, properties, duration, easingStyle, easingDirection)
    duration = duration or Settings.AnimationSpeed
    easingStyle = easingStyle or Enum.EasingStyle.Quad
    easingDirection = easingDirection or Enum.EasingDirection.Out

    if not Settings.EnableAnimations then
        for property, value in pairs(properties) do
            object[property] = value
        end
        return
    end

    local tween = TweenService:Create(object, TweenInfo.new(duration, easingStyle, easingDirection), properties)
    tween:Play()
    return tween
end

function AnimationUtils:FadeIn(object, duration)
    local originalTransparency = object.BackgroundTransparency
    object.BackgroundTransparency = 1
    return self:Tween(object, {BackgroundTransparency = originalTransparency}, duration)
end

function AnimationUtils:FadeOut(object, duration)
    return self:Tween(object, {BackgroundTransparency = 1}, duration)
end

function AnimationUtils:SlideIn(object, direction, duration)
    direction = direction or "Left"
    local originalPosition = object.Position

    local startPositions = {
        Left = UDim2.new(-1, 0, originalPosition.Y.Scale, originalPosition.Y.Offset),
        Right = UDim2.new(2, 0, originalPosition.Y.Scale, originalPosition.Y.Offset),
        Top = UDim2.new(originalPosition.X.Scale, originalPosition.X.Offset, -1, 0),
        Bottom = UDim2.new(originalPosition.X.Scale, originalPosition.X.Offset, 2, 0)
    }

    object.Position = startPositions[direction] or startPositions.Left
    return self:Tween(object, {Position = originalPosition}, duration)
end

function AnimationUtils:Bounce(object, scale, duration)
    scale = scale or 1.1
    duration = duration or 0.1

    local originalSize = object.Size
    local bounceSize = UDim2.new(originalSize.X.Scale * scale, originalSize.X.Offset,
                                originalSize.Y.Scale * scale, originalSize.Y.Offset)

    local tween1 = self:Tween(object, {Size = bounceSize}, duration)
    tween1.Completed:Connect(function()
        self:Tween(object, {Size = originalSize}, duration)
    end)

    return tween1
end

-- Optimized Utility Functions
local ElementCache = {}
local ConnectionManager = {}

-- Enhanced element creation with caching and validation
local function CreateElement(className, properties, parent)
    local element = Instance.new(className)

    -- Apply properties efficiently
    if properties then
        for property, value in pairs(properties) do
            if property ~= "Parent" then
                local success, err = pcall(function()
                    element[property] = value
                end)
                if not success then
                    warn("Failed to set property " .. property .. " on " .. className .. ": " .. err)
                end
            end
        end
    end

    -- Set parent last for better performance
    if parent then
        element.Parent = parent
    elseif properties and properties.Parent then
        element.Parent = properties.Parent
    end

    return element
end

-- Connection manager for better memory management
function ConnectionManager:Add(name, connection)
    if not self[name] then
        self[name] = {}
    end
    table.insert(self[name], connection)
end

function ConnectionManager:Disconnect(name)
    if self[name] then
        for _, connection in ipairs(self[name]) do
            if connection and connection.Connected then
                connection:Disconnect()
            end
        end
        self[name] = nil
    end
end

function ConnectionManager:DisconnectAll()
    for name, _ in pairs(self) do
        if name ~= "Add" and name ~= "Disconnect" and name ~= "DisconnectAll" then
            self:Disconnect(name)
        end
    end
end

-- Enhanced card creation with hover effects and theme integration
local function CreateCard(parent, title, description, layoutOrder, cardType)
    cardType = cardType or "default"

    local Card = CreateElement("Frame", {
        Name = title .. "Card",
        BackgroundColor3 = THEME.SURFACE,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 100),
        LayoutOrder = layoutOrder or 1
    }, parent)

    -- Register for theme updates
    ThemeManager:RegisterElement(Card, "BackgroundColor3", "SURFACE")

    local CardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12)
    }, Card)

    local CardStroke = CreateElement("UIStroke", {
        Color = THEME.SURFACE_VARIANT,
        Thickness = 1,
        Transparency = 0.8
    }, Card)

    ThemeManager:RegisterElement(CardStroke, "Color", "SURFACE_VARIANT")

    local CardPadding = CreateElement("UIPadding", {
        PaddingLeft = UDim.new(0, 20),
        PaddingRight = UDim.new(0, 20),
        PaddingTop = UDim.new(0, 15),
        PaddingBottom = UDim.new(0, 15)
    }, Card)

    local TitleLabel = CreateElement("TextLabel", {
        Name = "TitleLabel",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, -80, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = title,
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        RichText = true
    }, Card)

    ThemeManager:RegisterElement(TitleLabel, "TextColor3", "ON_SURFACE")

    local DescLabel = CreateElement("TextLabel", {
        Name = "DescLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 28),
        Size = UDim2.new(1, -80, 0, 20),
        Font = Enum.Font.Gotham,
        Text = description,
        TextColor3 = THEME.SURFACE_VARIANT,
        TextSize = 13,
        TextXAlignment = Enum.TextXAlignment.Left,
        TextWrapped = true
    }, Card)

    ThemeManager:RegisterElement(DescLabel, "TextColor3", "SURFACE_VARIANT")

    local StatusLabel = CreateElement("TextLabel", {
        Name = "StatusLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 55),
        Size = UDim2.new(0.7, 0, 0, 20),
        Font = Enum.Font.GothamMedium,
        Text = "Status: Idle",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 12,
        TextXAlignment = Enum.TextXAlignment.Left
    }, Card)

    ThemeManager:RegisterElement(StatusLabel, "TextColor3", "ON_SURFACE")

    -- Add hover effects
    local isHovering = false

    Card.MouseEnter:Connect(function()
        if not isHovering then
            isHovering = true
            AnimationUtils:Tween(Card, {
                BackgroundTransparency = 0.05,
                Size = UDim2.new(1, 2, 0, 102)
            }, 0.2)
            AnimationUtils:Tween(CardStroke, {Transparency = 0.4}, 0.2)
        end
    end)

    Card.MouseLeave:Connect(function()
        if isHovering then
            isHovering = false
            AnimationUtils:Tween(Card, {
                BackgroundTransparency = 0.1,
                Size = UDim2.new(1, 0, 0, 100)
            }, 0.2)
            AnimationUtils:Tween(CardStroke, {Transparency = 0.8}, 0.2)
        end
    end)

    return Card, TitleLabel, DescLabel, StatusLabel
end

-- Enhanced toggle with smooth animations and theme integration
local function CreateToggle(parent, anchorPoint, position, initialState)
    initialState = initialState or false

    local Toggle = CreateElement("Frame", {
        Name = "Toggle",
        AnchorPoint = anchorPoint or Vector2.new(1, 0.5),
        BackgroundColor3 = initialState and THEME.SUCCESS or THEME.SURFACE_VARIANT,
        BorderSizePixel = 0,
        Position = position or UDim2.new(1, -10, 0.5, 0),
        Size = UDim2.new(0, 54, 0, 28)
    }, parent)

    -- Register for theme updates
    ThemeManager:RegisterElement(Toggle, "BackgroundColor3", initialState and "SUCCESS" or "SURFACE_VARIANT")

    local ToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0)
    }, Toggle)

    local ToggleStroke = CreateElement("UIStroke", {
        Color = THEME.SURFACE_VARIANT,
        Thickness = 2,
        Transparency = 0.6
    }, Toggle)

    ThemeManager:RegisterElement(ToggleStroke, "Color", "SURFACE_VARIANT")

    local ToggleCircle = CreateElement("Frame", {
        Name = "ToggleCircle",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME.ON_SURFACE,
        Position = initialState and UDim2.new(1, -14, 0.5, 0) or UDim2.new(0, 14, 0.5, 0),
        Size = UDim2.new(0, 20, 0, 20)
    }, Toggle)

    ThemeManager:RegisterElement(ToggleCircle, "BackgroundColor3", "ON_SURFACE")

    local ToggleCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0)
    }, ToggleCircle)

    local ToggleCircleShadow = CreateElement("ImageLabel", {
        Name = "Shadow",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(0.5, 2, 0.5, 2),
        Size = UDim2.new(1, 4, 1, 4),
        Image = "rbxasset://textures/ui/GuiImagePlaceholder.png",
        ImageColor3 = Color3.fromRGB(0, 0, 0),
        ImageTransparency = 0.8,
        ZIndex = -1
    }, ToggleCircle)

    -- Enhanced toggle animation
    local function AnimateToggle(enabled)
        local targetPos = enabled and UDim2.new(1, -14, 0.5, 0) or UDim2.new(0, 14, 0.5, 0)
        local targetColor = enabled and THEME.SUCCESS or THEME.SURFACE_VARIANT
        local targetStrokeColor = enabled and THEME.SUCCESS or THEME.SURFACE_VARIANT

        -- Animate circle position with bounce effect
        AnimationUtils:Tween(ToggleCircle, {Position = targetPos}, 0.25, Enum.EasingStyle.Back, Enum.EasingDirection.Out)

        -- Animate background color
        AnimationUtils:Tween(Toggle, {BackgroundColor3 = targetColor}, 0.2)

        -- Animate stroke
        AnimationUtils:Tween(ToggleStroke, {
            Color = targetStrokeColor,
            Transparency = enabled and 0.3 or 0.6
        }, 0.2)

        -- Scale animation for feedback
        AnimationUtils:Bounce(ToggleCircle, 1.15, 0.1)
    end

    -- Store animation function for external use
    Toggle.AnimateToggle = AnimateToggle

    return Toggle, ToggleCircle, AnimateToggle
end

-- Module System
local ModuleSystem = {}
ModuleSystem.Modules = {}
ModuleSystem.ActiveModules = {}

function ModuleSystem:RegisterModule(moduleData)
    self.Modules[moduleData.Name] = moduleData
    Settings.ModuleSettings[moduleData.Name] = moduleData.DefaultSettings or {}
end

function ModuleSystem:GetModule(name)
    return self.Modules[name]
end

function ModuleSystem:StartModule(name)
    local module = self.Modules[name]
    if module and not self.ActiveModules[name] then
        if module.Start then
            self.ActiveModules[name] = module.Start(Settings.ModuleSettings[name])
        end
    end
end

function ModuleSystem:StopModule(name)
    local module = self.Modules[name]
    if module and self.ActiveModules[name] then
        if module.Stop then
            module.Stop(self.ActiveModules[name])
        end
        self.ActiveModules[name] = nil
    end
end

function ModuleSystem:IsModuleActive(name)
    return self.ActiveModules[name] ~= nil
end

-- Enhanced Base GUI Framework with modern design
local function CreateBaseGUI()
    -- Check if GUI already exists and destroy it
    local existingGui = PlayerGui:FindFirstChild("RobloxScriptHub")
    if existingGui then
        existingGui:Destroy()
    end

    local ScreenGui = CreateElement("ScreenGui", {
        Name = "RobloxScriptHub",
        ResetOnSpawn = false,
        ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
        IgnoreGuiInset = true
    }, PlayerGui)

    local MainFrame = CreateElement("Frame", {
        Name = "MainFrame",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME.BACKGROUND,
        BackgroundTransparency = Settings.Transparency,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 0, 0.5, 0),
        Size = Settings.UISize,
        ClipsDescendants = true
    }, ScreenGui)

    -- Register for theme updates
    ThemeManager:RegisterElement(MainFrame, "BackgroundColor3", "BACKGROUND")

    local UICorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 16)
    }, MainFrame)

    -- Add drop shadow effect
    local ShadowFrame = CreateElement("Frame", {
        Name = "ShadowFrame",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = Color3.fromRGB(0, 0, 0),
        BackgroundTransparency = 0.7,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 4, 0.5, 4),
        Size = Settings.UISize,
        ZIndex = -1
    }, ScreenGui)

    local ShadowCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 16)
    }, ShadowFrame)

    -- Add subtle gradient overlay
    local GradientFrame = CreateElement("Frame", {
        Name = "GradientFrame",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 1, 0),
        ZIndex = 1
    }, MainFrame)

    local UIGradient = CreateElement("UIGradient", {
        Color = ColorSequence.new({
            ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 255)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(200, 200, 200))
        }),
        Rotation = 45,
        Transparency = NumberSequence.new({
            NumberSequenceKeypoint.new(0, 0.95),
            NumberSequenceKeypoint.new(1, 0.98)
        })
    }, GradientFrame)

    -- Enhanced Title Bar with modern design
    local TitleBar = CreateElement("Frame", {
        Name = "TitleBar",
        BackgroundColor3 = THEME.PRIMARY,
        BackgroundTransparency = 0.05,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 50),
        ZIndex = 2
    }, MainFrame)

    -- Register for theme updates
    ThemeManager:RegisterElement(TitleBar, "BackgroundColor3", "PRIMARY")

    local TitleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 16)
    }, TitleBar)

    local TitleCornerFix = CreateElement("Frame", {
        Name = "TitleCornerFix",
        BackgroundColor3 = THEME.PRIMARY,
        BackgroundTransparency = 0.05,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0.6, 0),
        Size = UDim2.new(1, 0, 0.4, 0)
    }, TitleBar)

    ThemeManager:RegisterElement(TitleCornerFix, "BackgroundColor3", "PRIMARY")

    -- Add title bar gradient
    local TitleGradient = CreateElement("UIGradient", {
        Color = ColorSequence.new({
            ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 255)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(240, 240, 240))
        }),
        Rotation = 90,
        Transparency = NumberSequence.new({
            NumberSequenceKeypoint.new(0, 0.9),
            NumberSequenceKeypoint.new(1, 0.95)
        })
    }, TitleBar)

    local TitleText = CreateElement("TextLabel", {
        Name = "TitleText",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 20, 0, 0),
        Size = UDim2.new(1, -120, 1, 0),
        Font = Enum.Font.GothamBold,
        Text = "🚀 Roblox Script Hub",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 20,
        TextXAlignment = Enum.TextXAlignment.Left,
        RichText = true
    }, TitleBar)

    ThemeManager:RegisterElement(TitleText, "TextColor3", "ON_SURFACE")

    -- Enhanced close button with hover effects
    local CloseButton = CreateElement("TextButton", {
        Name = "CloseButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME.ERROR,
        BackgroundTransparency = 0.8,
        BorderSizePixel = 0,
        Position = UDim2.new(1, -15, 0.5, 0),
        Size = UDim2.new(0, 32, 0, 32),
        Font = Enum.Font.GothamBold,
        Text = "✕",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 14
    }, TitleBar)

    ThemeManager:RegisterElement(CloseButton, "BackgroundColor3", "ERROR")
    ThemeManager:RegisterElement(CloseButton, "TextColor3", "ON_SURFACE")

    local CloseCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8)
    }, CloseButton)

    -- Enhanced minimize button
    local MinimizeButton = CreateElement("TextButton", {
        Name = "MinimizeButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME.WARNING,
        BackgroundTransparency = 0.8,
        BorderSizePixel = 0,
        Position = UDim2.new(1, -55, 0.5, 0),
        Size = UDim2.new(0, 32, 0, 32),
        Font = Enum.Font.GothamBold,
        Text = "−",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 16
    }, TitleBar)

    ThemeManager:RegisterElement(MinimizeButton, "BackgroundColor3", "WARNING")
    ThemeManager:RegisterElement(MinimizeButton, "TextColor3", "ON_SURFACE")

    local MinimizeCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8)
    }, MinimizeButton)

    -- Add hover effects for buttons
    local function AddButtonHoverEffect(button, hoverTransparency, normalTransparency)
        normalTransparency = normalTransparency or 0.8
        hoverTransparency = hoverTransparency or 0.3

        button.MouseEnter:Connect(function()
            AnimationUtils:Tween(button, {BackgroundTransparency = hoverTransparency}, 0.2)
            AnimationUtils:Bounce(button, 1.1, 0.1)
        end)

        button.MouseLeave:Connect(function()
            AnimationUtils:Tween(button, {BackgroundTransparency = normalTransparency}, 0.2)
        end)
    end

    AddButtonHoverEffect(CloseButton, 0.2, 0.8)
    AddButtonHoverEffect(MinimizeButton, 0.2, 0.8)

    -- Enhanced Main Layout
    local MainLayout = CreateElement("Frame", {
        Name = "MainLayout",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 50),
        Size = UDim2.new(1, 0, 1, -50),
        ClipsDescendants = true
    }, MainFrame)

    -- Enhanced Side Menu with modern design
    local MenuContainer = CreateElement("Frame", {
        Name = "MenuContainer",
        BackgroundColor3 = THEME.SECONDARY,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Size = UDim2.new(0, 160, 1, 0)
    }, MainLayout)

    -- Register for theme updates
    ThemeManager:RegisterElement(MenuContainer, "BackgroundColor3", "SECONDARY")

    -- Add subtle border
    local MenuBorder = CreateElement("Frame", {
        Name = "MenuBorder",
        AnchorPoint = Vector2.new(1, 0),
        BackgroundColor3 = THEME.SURFACE_VARIANT,
        BorderSizePixel = 0,
        Position = UDim2.new(1, 0, 0, 0),
        Size = UDim2.new(0, 1, 1, 0)
    }, MenuContainer)

    ThemeManager:RegisterElement(MenuBorder, "BackgroundColor3", "SURFACE_VARIANT")

    local MenuPadding = CreateElement("UIPadding", {
        PaddingTop = UDim.new(0, 20),
        PaddingLeft = UDim.new(0, 15),
        PaddingRight = UDim.new(0, 15),
        PaddingBottom = UDim.new(0, 20)
    }, MenuContainer)

    local MenuList = CreateElement("UIListLayout", {
        Padding = UDim.new(0, 10),
        HorizontalAlignment = Enum.HorizontalAlignment.Center,
        SortOrder = Enum.SortOrder.LayoutOrder
    }, MenuContainer)

    -- Enhanced Content Area
    local ContentArea = CreateElement("Frame", {
        Name = "ContentArea",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 160, 0, 0),
        Size = UDim2.new(1, -160, 1, 0),
        ClipsDescendants = true
    }, MainLayout)

    -- Add content area background with subtle pattern
    local ContentBackground = CreateElement("Frame", {
        Name = "ContentBackground",
        BackgroundColor3 = THEME.BACKGROUND,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 1, 0)
    }, ContentArea)

    ThemeManager:RegisterElement(ContentBackground, "BackgroundColor3", "BACKGROUND")

    -- Add subtle texture pattern
    local PatternFrame = CreateElement("Frame", {
        Name = "PatternFrame",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 1, 0)
    }, ContentBackground)

    local PatternGradient = CreateElement("UIGradient", {
        Color = ColorSequence.new({
            ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 255)),
            ColorSequenceKeypoint.new(0.5, Color3.fromRGB(250, 250, 250)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(245, 245, 245))
        }),
        Rotation = 45,
        Transparency = NumberSequence.new({
            NumberSequenceKeypoint.new(0, 0.98),
            NumberSequenceKeypoint.new(0.5, 0.99),
            NumberSequenceKeypoint.new(1, 0.98)
        })
    }, PatternFrame)

    -- Enhanced Dragging Functionality with smooth movement
    local Dragging = false
    local DragInput = nil
    local DragStart = nil
    local StartPos = nil
    local DragConnection = nil

    local function UpdateDrag()
        if Dragging and DragInput then
            local Delta = DragInput.Position - DragStart
            local newPosition = UDim2.new(
                StartPos.X.Scale,
                StartPos.X.Offset + Delta.X,
                StartPos.Y.Scale,
                StartPos.Y.Offset + Delta.Y
            )

            -- Smooth dragging with constraints
            if Settings.EnableAnimations then
                AnimationUtils:Tween(MainFrame, {Position = newPosition}, 0.1, Enum.EasingStyle.Quad)
                AnimationUtils:Tween(ShadowFrame, {Position = UDim2.new(
                    newPosition.X.Scale, newPosition.X.Offset + 4,
                    newPosition.Y.Scale, newPosition.Y.Offset + 4
                )}, 0.1, Enum.EasingStyle.Quad)
            else
                MainFrame.Position = newPosition
                ShadowFrame.Position = UDim2.new(
                    newPosition.X.Scale, newPosition.X.Offset + 4,
                    newPosition.Y.Scale, newPosition.Y.Offset + 4
                )
            end
        end
    end

    -- Register drag connection for cleanup
    local dragConnection = TitleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
            Dragging = true
            DragStart = input.Position
            StartPos = MainFrame.Position

            -- Visual feedback for dragging
            AnimationUtils:Tween(MainFrame, {BackgroundTransparency = Settings.Transparency + 0.1}, 0.2)

            DragConnection = RunService.RenderStepped:Connect(function()
                if not UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) and
                   not UserInputService:IsMouseButtonPressed(Enum.UserInputType.Touch) then
                    Dragging = false

                    -- Reset visual feedback
                    AnimationUtils:Tween(MainFrame, {BackgroundTransparency = Settings.Transparency}, 0.2)

                    if DragConnection then
                        DragConnection:Disconnect()
                        DragConnection = nil
                    end
                end
            end)
        end
    end)

    ConnectionManager:Add("Drag", dragConnection)

    local inputConnection = UserInputService.InputChanged:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseMovement or input.UserInputType == Enum.UserInputType.Touch then
            DragInput = input
            if Dragging then
                UpdateDrag()
            end
        end
    end)

    ConnectionManager:Add("Input", inputConnection)

    -- Enhanced Close and Minimize Functionality
    local closeConnection = CloseButton.MouseButton1Click:Connect(function()
        -- Smooth close animation
        AnimationUtils:FadeOut(MainFrame, 0.3)
        AnimationUtils:FadeOut(ShadowFrame, 0.3)

        -- Stop all active modules
        for name, _ in pairs(ModuleSystem.ActiveModules) do
            ModuleSystem:StopModule(name)
        end

        -- Cleanup all connections
        ConnectionManager:DisconnectAll()

        -- Destroy GUI after animation
        wait(0.3)
        ScreenGui:Destroy()
    end)

    ConnectionManager:Add("Close", closeConnection)

    local minimizeConnection = MinimizeButton.MouseButton1Click:Connect(function()
        Settings.MinimizedState = not Settings.MinimizedState

        if Settings.MinimizedState then
            -- Minimize animation
            AnimationUtils:Tween(MainFrame, {
                Size = UDim2.new(0, 400, 0, 50)
            }, 0.4, Enum.EasingStyle.Back, Enum.EasingDirection.InOut)

            AnimationUtils:Tween(ShadowFrame, {
                Size = UDim2.new(0, 400, 0, 50)
            }, 0.4, Enum.EasingStyle.Back, Enum.EasingDirection.InOut)

            -- Hide main layout with fade
            AnimationUtils:FadeOut(MainLayout, 0.2)
            wait(0.2)
            MainLayout.Visible = false

            -- Update minimize button text
            MinimizeButton.Text = "□"
        else
            -- Restore animation
            MainLayout.Visible = true
            AnimationUtils:FadeIn(MainLayout, 0.2)

            AnimationUtils:Tween(MainFrame, {
                Size = Settings.UISize
            }, 0.4, Enum.EasingStyle.Back, Enum.EasingDirection.InOut)

            AnimationUtils:Tween(ShadowFrame, {
                Size = Settings.UISize
            }, 0.4, Enum.EasingStyle.Back, Enum.EasingDirection.InOut)

            -- Update minimize button text
            MinimizeButton.Text = "−"
        end
    end)

    ConnectionManager:Add("Minimize", minimizeConnection)

    -- Add entrance animation
    MainFrame.Size = UDim2.new(0, 0, 0, 0)
    ShadowFrame.Size = UDim2.new(0, 0, 0, 0)
    MainFrame.BackgroundTransparency = 1

    AnimationUtils:Tween(MainFrame, {
        Size = Settings.UISize,
        BackgroundTransparency = Settings.Transparency
    }, 0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)

    AnimationUtils:Tween(ShadowFrame, {
        Size = Settings.UISize
    }, 0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)

    return ScreenGui, MainFrame, TitleBar, CloseButton, MinimizeButton, MenuContainer, ContentArea
end

-- Enhanced Tab System with modern design and smooth transitions
local function CreateTabSystem(menuContainer, contentArea)
    local MenuItems = {
        {Name = "Home", Icon = "🏠", Order = 1, Description = "Dashboard and overview"},
        {Name = "AutoClick", Icon = "🖱️", Order = 2, Description = "Automated clicking features"},
        {Name = "AutoFarm", Icon = "🌾", Order = 3, Description = "Resource farming automation"},
        {Name = "Player", Icon = "👤", Order = 4, Description = "Player enhancement tools"},
        {Name = "Settings", Icon = "⚙️", Order = 5, Description = "Configuration and preferences"}
    }

    local MenuButtons = {}
    local ContentFrames = {}
    local TabIndicator = nil

    -- Create tab indicator
    TabIndicator = CreateElement("Frame", {
        Name = "TabIndicator",
        BackgroundColor3 = THEME.PRIMARY,
        BorderSizePixel = 0,
        Size = UDim2.new(0, 4, 0, 45),
        Position = UDim2.new(0, 0, 0, 20)
    }, menuContainer)

    ThemeManager:RegisterElement(TabIndicator, "BackgroundColor3", "PRIMARY")

    local IndicatorCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 2)
    }, TabIndicator)

    for _, item in ipairs(MenuItems) do
        -- Create Enhanced Menu Button
        local MenuButton = CreateElement("TextButton", {
            Name = item.Name .. "Button",
            BackgroundColor3 = Settings.ActiveTab == item.Name and THEME.PRIMARY or THEME.SECONDARY,
            BackgroundTransparency = Settings.ActiveTab == item.Name and 0.2 or 0.8,
            BorderSizePixel = 0,
            Size = UDim2.new(1, -10, 0, 45),
            Font = Enum.Font.GothamSemibold,
            Text = "",
            TextColor3 = THEME.ON_SURFACE,
            TextSize = 14,
            LayoutOrder = item.Order
        }, menuContainer)

        -- Register for theme updates
        ThemeManager:RegisterElement(MenuButton, "BackgroundColor3", Settings.ActiveTab == item.Name and "PRIMARY" or "SECONDARY")
        ThemeManager:RegisterElement(MenuButton, "TextColor3", "ON_SURFACE")

        local ButtonCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 12)
        }, MenuButton)

        -- Create button content layout
        local ButtonContent = CreateElement("Frame", {
            Name = "ButtonContent",
            BackgroundTransparency = 1,
            Size = UDim2.new(1, 0, 1, 0)
        }, MenuButton)

        local ButtonPadding = CreateElement("UIPadding", {
            PaddingLeft = UDim.new(0, 15),
            PaddingRight = UDim.new(0, 15),
            PaddingTop = UDim.new(0, 8),
            PaddingBottom = UDim.new(0, 8)
        }, ButtonContent)

        local IconLabel = CreateElement("TextLabel", {
            Name = "IconLabel",
            BackgroundTransparency = 1,
            Size = UDim2.new(0, 20, 0, 20),
            Position = UDim2.new(0, 0, 0, 0),
            Font = Enum.Font.GothamBold,
            Text = item.Icon,
            TextColor3 = THEME.ON_SURFACE,
            TextSize = 16,
            TextXAlignment = Enum.TextXAlignment.Center,
            TextYAlignment = Enum.TextYAlignment.Center
        }, ButtonContent)

        ThemeManager:RegisterElement(IconLabel, "TextColor3", "ON_SURFACE")

        local NameLabel = CreateElement("TextLabel", {
            Name = "NameLabel",
            BackgroundTransparency = 1,
            Position = UDim2.new(0, 25, 0, -2),
            Size = UDim2.new(1, -25, 0, 20),
            Font = Enum.Font.GothamSemibold,
            Text = item.Name,
            TextColor3 = THEME.ON_SURFACE,
            TextSize = 13,
            TextXAlignment = Enum.TextXAlignment.Left,
            TextYAlignment = Enum.TextYAlignment.Center
        }, ButtonContent)

        ThemeManager:RegisterElement(NameLabel, "TextColor3", "ON_SURFACE")

        -- Create Enhanced Content Frame
        local ContentFrame = CreateElement("ScrollingFrame", {
            Name = item.Name .. "Content",
            BackgroundTransparency = 1,
            BorderSizePixel = 0,
            Size = UDim2.new(1, 0, 1, 0),
            CanvasSize = UDim2.new(0, 0, 0, 0),
            ScrollBarThickness = 8,
            ScrollingDirection = Enum.ScrollingDirection.Y,
            VerticalScrollBarInset = Enum.ScrollBarInset.ScrollBar,
            ScrollBarImageColor3 = THEME.SURFACE_VARIANT,
            ScrollBarImageTransparency = 0.3,
            Visible = Settings.ActiveTab == item.Name,
            ClipsDescendants = true
        }, contentArea)

        -- Register scrollbar for theme updates
        ThemeManager:RegisterElement(ContentFrame, "ScrollBarImageColor3", "SURFACE_VARIANT")

        local ContentPadding = CreateElement("UIPadding", {
            PaddingLeft = UDim.new(0, 25),
            PaddingRight = UDim.new(0, 25),
            PaddingTop = UDim.new(0, 25),
            PaddingBottom = UDim.new(0, 25)
        }, ContentFrame)

        local ContentList = CreateElement("UIListLayout", {
            Padding = UDim.new(0, 15),
            SortOrder = Enum.SortOrder.LayoutOrder,
            HorizontalAlignment = Enum.HorizontalAlignment.Center
        }, ContentFrame)

        MenuButtons[item.Name] = MenuButton
        ContentFrames[item.Name] = ContentFrame

        -- Enhanced Tab Switching Logic with animations
        local buttonConnection = MenuButton.MouseButton1Click:Connect(function()
            if Settings.ActiveTab == item.Name then return end -- Don't switch to same tab

            local previousTab = Settings.ActiveTab
            Settings.ActiveTab = item.Name

            -- Animate tab indicator
            local buttonPosition = MenuButton.AbsolutePosition.Y - menuContainer.AbsolutePosition.Y
            AnimationUtils:Tween(TabIndicator, {
                Position = UDim2.new(0, 0, 0, buttonPosition)
            }, 0.3, Enum.EasingStyle.Quart, Enum.EasingDirection.Out)

            -- Update button appearances
            for name, button in pairs(MenuButtons) do
                local isActive = name == item.Name
                local targetColor = isActive and THEME.PRIMARY or THEME.SECONDARY
                local targetTransparency = isActive and 0.2 or 0.8

                AnimationUtils:Tween(button, {
                    BackgroundColor3 = targetColor,
                    BackgroundTransparency = targetTransparency
                }, 0.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)

                -- Update theme registration
                ThemeManager:RegisterElement(button, "BackgroundColor3", isActive and "PRIMARY" or "SECONDARY")
            end

            -- Smooth content transition
            local previousFrame = ContentFrames[previousTab]
            local currentFrame = ContentFrames[item.Name]

            if previousFrame then
                AnimationUtils:SlideIn(previousFrame, "Left", 0.2)
                AnimationUtils:FadeOut(previousFrame, 0.15)
                wait(0.15)
                previousFrame.Visible = false
            end

            if currentFrame then
                currentFrame.Visible = true
                AnimationUtils:SlideIn(currentFrame, "Right", 0.25)
                AnimationUtils:FadeIn(currentFrame, 0.2)
            end

            -- Bounce effect for feedback
            AnimationUtils:Bounce(MenuButton, 1.05, 0.1)
        end)

        ConnectionManager:Add("TabButton_" .. item.Name, buttonConnection)

        -- Add hover effects
        MenuButton.MouseEnter:Connect(function()
            if Settings.ActiveTab ~= item.Name then
                AnimationUtils:Tween(MenuButton, {
                    BackgroundTransparency = 0.6,
                    Size = UDim2.new(1, -8, 0, 45)
                }, 0.2)
            end
        end)

        MenuButton.MouseLeave:Connect(function()
            if Settings.ActiveTab ~= item.Name then
                AnimationUtils:Tween(MenuButton, {
                    BackgroundTransparency = 0.8,
                    Size = UDim2.new(1, -10, 0, 45)
                }, 0.2)
            end
        end)
    end

    -- Set initial tab indicator position
    if MenuButtons[Settings.ActiveTab] then
        local activeButton = MenuButtons[Settings.ActiveTab]
        local buttonPosition = (Settings.ActiveTab == "Home" and 0) or
                              (Settings.ActiveTab == "AutoClick" and 55) or
                              (Settings.ActiveTab == "AutoFarm" and 110) or
                              (Settings.ActiveTab == "Player" and 165) or
                              (Settings.ActiveTab == "Settings" and 220) or 0
        TabIndicator.Position = UDim2.new(0, 0, 0, buttonPosition + 20)
    end

    return MenuButtons, ContentFrames, TabIndicator
end

-- Feature Modules
local Modules = {}

-- AutoClick Module
Modules.AutoClick = {
    Name = "AutoClick",
    DisplayName = "Auto Click",
    Description = "Automatically clicks for you",
    DefaultSettings = {
        Enabled = false,
        Interval = 0.005,
        RemotePath = "ReplicatedStorage.Remotes.Click"
    },

    Start = function(settings)
        local connection
        local remote = nil

        -- Try to find the remote
        local success, result = pcall(function()
            local parts = string.split(settings.RemotePath, ".")
            local current = game
            for _, part in ipairs(parts) do
                current = current:WaitForChild(part, 5)
            end
            return current
        end)

        if success then
            remote = result
        else
            warn("AutoClick: Could not find remote at " .. settings.RemotePath)
            return nil
        end

        connection = RunService.Heartbeat:Connect(function()
            if settings.Enabled and remote then
                remote:FireServer()
                wait(settings.Interval)
            end
        end)

        return {
            Connection = connection,
            Remote = remote
        }
    end,

    Stop = function(moduleData)
        if moduleData and moduleData.Connection then
            moduleData.Connection:Disconnect()
        end
    end
}

-- AutoFarm Module
Modules.AutoFarm = {
    Name = "AutoFarm",
    DisplayName = "Auto Farm",
    Description = "Automatically farms resources",
    DefaultSettings = {
        Enabled = false,
        FarmCoins = false,
        FarmGems = false,
        FarmXP = false,
        Interval = 0.1
    },

    Start = function(settings)
        local connections = {}

        -- Coin farming
        if settings.FarmCoins then
            connections.Coins = RunService.Heartbeat:Connect(function()
                if settings.Enabled then
                    for _, coin in ipairs(workspace:GetChildren()) do
                        if coin.Name == "Coin" and coin:FindFirstChild("TouchInterest") then
                            local humanoidRootPart = Player.Character and Player.Character:FindFirstChild("HumanoidRootPart")
                            if humanoidRootPart then
                                firetouchinterest(humanoidRootPart, coin, 0)
                                firetouchinterest(humanoidRootPart, coin, 1)
                            end
                        end
                    end
                    wait(settings.Interval)
                end
            end)
        end

        return {
            Connections = connections
        }
    end,

    Stop = function(moduleData)
        if moduleData and moduleData.Connections then
            for _, connection in pairs(moduleData.Connections) do
                connection:Disconnect()
            end
        end
    end
}

-- Player Module
Modules.Player = {
    Name = "Player",
    DisplayName = "Player Tools",
    Description = "Player enhancement tools",
    DefaultSettings = {
        WalkSpeed = 16,
        JumpPower = 50,
        NoClip = false,
        InfiniteJump = false
    },

    Start = function(settings)
        local connections = {}
        local character = Player.Character or Player.CharacterAdded:Wait()
        local humanoid = character:WaitForChild("Humanoid")

        -- Apply speed and jump settings
        humanoid.WalkSpeed = settings.WalkSpeed
        humanoid.JumpPower = settings.JumpPower

        -- NoClip
        if settings.NoClip then
            connections.NoClip = RunService.Stepped:Connect(function()
                if Player.Character then
                    for _, part in pairs(Player.Character:GetChildren()) do
                        if part:IsA("BasePart") then
                            part.CanCollide = false
                        end
                    end
                end
            end)
        end

        -- Infinite Jump
        if settings.InfiniteJump then
            connections.InfiniteJump = UserInputService.JumpRequest:Connect(function()
                if Player.Character and Player.Character:FindFirstChild("Humanoid") then
                    Player.Character.Humanoid:ChangeState(Enum.HumanoidStateType.Jumping)
                end
            end)
        end

        return {
            Connections = connections,
            Humanoid = humanoid
        }
    end,

    Stop = function(moduleData)
        if moduleData then
            if moduleData.Connections then
                for _, connection in pairs(moduleData.Connections) do
                    connection:Disconnect()
                end
            end
            if moduleData.Humanoid then
                moduleData.Humanoid.WalkSpeed = 16
                moduleData.Humanoid.JumpPower = 50
            end
        end
    end
}

-- Register all modules
for _, module in pairs(Modules) do
    ModuleSystem:RegisterModule(module)
end

-- Content Creation Functions
local function CreateHomeContent(contentFrame)
    -- Welcome Card
    local WelcomeCard = CreateElement("Frame", {
        Name = "WelcomeCard",
        BackgroundColor3 = THEME.SURFACE,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 120),
        LayoutOrder = 1
    }, contentFrame)

    -- Register for theme updates
    ThemeManager:RegisterElement(WelcomeCard, "BackgroundColor3", "SURFACE")

    local WelcomeCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12)
    }, WelcomeCard)

    local WelcomePadding = CreateElement("UIPadding", {
        PaddingAll = UDim.new(0, 20)
    }, WelcomeCard)

    local WelcomeTitle = CreateElement("TextLabel", {
        Name = "WelcomeTitle",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 30),
        Font = Enum.Font.GothamBold,
        Text = "🎉 Welcome to Roblox Script Hub!",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 20,
        TextXAlignment = Enum.TextXAlignment.Left
    }, WelcomeCard)

    ThemeManager:RegisterElement(WelcomeTitle, "TextColor3", "ON_SURFACE")

    local WelcomeDesc = CreateElement("TextLabel", {
        Name = "WelcomeDesc",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 35),
        Size = UDim2.new(1, 0, 1, -35),
        Font = Enum.Font.Gotham,
        Text = "A modular script hub with various automation features.\nSelect a tab from the menu to get started!",
        TextColor3 = THEME.SURFACE_VARIANT,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        TextYAlignment = Enum.TextYAlignment.Top,
        TextWrapped = true
    }, WelcomeCard)

    ThemeManager:RegisterElement(WelcomeDesc, "TextColor3", "SURFACE_VARIANT")

    -- Stats Card
    local StatsCard = CreateElement("Frame", {
        Name = "StatsCard",
        BackgroundColor3 = THEME.SURFACE,
        BackgroundTransparency = 0.1,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 100),
        LayoutOrder = 2
    }, contentFrame)

    ThemeManager:RegisterElement(StatsCard, "BackgroundColor3", "SURFACE")

    local StatsCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 12)
    }, StatsCard)

    local StatsPadding = CreateElement("UIPadding", {
        PaddingAll = UDim.new(0, 20)
    }, StatsCard)

    local StatsTitle = CreateElement("TextLabel", {
        Name = "StatsTitle",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = "📊 Active Modules",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left
    }, StatsCard)

    ThemeManager:RegisterElement(StatsTitle, "TextColor3", "ON_SURFACE")

    local ActiveModulesLabel = CreateElement("TextLabel", {
        Name = "ActiveModulesLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 30),
        Size = UDim2.new(1, 0, 1, -30),
        Font = Enum.Font.Gotham,
        Text = "No modules currently active",
        TextColor3 = THEME.SURFACE_VARIANT,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        TextYAlignment = Enum.TextYAlignment.Top
    }, StatsCard)

    ThemeManager:RegisterElement(ActiveModulesLabel, "TextColor3", "SURFACE_VARIANT")

    -- Update active modules display
    spawn(function()
        while wait(1) do
            if not contentFrame.Parent then break end

            local activeCount = 0
            local activeNames = {}
            for name, _ in pairs(ModuleSystem.ActiveModules) do
                activeCount = activeCount + 1
                table.insert(activeNames, name)
            end

            if activeCount > 0 then
                ActiveModulesLabel.Text = "Active: " .. table.concat(activeNames, ", ")
                ActiveModulesLabel.TextColor3 = THEME.SUCCESS
            else
                ActiveModulesLabel.Text = "No modules currently active"
                ActiveModulesLabel.TextColor3 = THEME.SURFACE_VARIANT
            end
        end
    end)
end

local function CreateModuleContent(contentFrame, moduleName)
    local module = ModuleSystem:GetModule(moduleName)
    if not module then return end

    local settings = Settings.ModuleSettings[moduleName]

    -- Module Card
    local Card, TitleLabel, DescLabel, StatusLabel = CreateCard(contentFrame, module.DisplayName, module.Description, 1)
    local Toggle, ToggleCircle = CreateToggle(Card)

    -- Update toggle function
    local function UpdateToggle()
        local isActive = ModuleSystem:IsModuleActive(moduleName)
        local TogglePos = isActive and UDim2.new(1, -22, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = isActive and THEME.SUCCESS or THEME.SURFACE_VARIANT

        TweenService:Create(ToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(Toggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        StatusLabel.Text = "Status: " .. (isActive and "Active" or "Idle")
        StatusLabel.TextColor3 = isActive and THEME.SUCCESS or THEME.ON_SURFACE
    end

    -- Toggle functionality
    Toggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            if ModuleSystem:IsModuleActive(moduleName) then
                ModuleSystem:StopModule(moduleName)
                settings.Enabled = false
            else
                settings.Enabled = true
                ModuleSystem:StartModule(moduleName)
            end
            UpdateToggle()
        end
    end)

    -- Module-specific settings
    if moduleName == "AutoClick" then
        -- Interval Setting
        local IntervalCard = CreateElement("Frame", {
            Name = "IntervalCard",
            BackgroundColor3 = THEME.SURFACE,
            BackgroundTransparency = 0.3,
            BorderSizePixel = 0,
            Size = UDim2.new(1, 0, 0, 80),
            LayoutOrder = 2,
            Parent = contentFrame
        })

        local IntervalCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 8),
            Parent = IntervalCard
        })

        local IntervalPadding = CreateElement("UIPadding", {
            PaddingAll = UDim.new(0, 15),
            Parent = IntervalCard
        })

        local IntervalTitle = CreateElement("TextLabel", {
            Name = "IntervalTitle",
            BackgroundTransparency = 1,
            Size = UDim2.new(1, 0, 0, 20),
            Font = Enum.Font.GothamBold,
            Text = "Click Interval (seconds)",
            TextColor3 = THEME.ON_SURFACE,
            TextSize = 14,
            TextXAlignment = Enum.TextXAlignment.Left,
            Parent = IntervalCard
        })

        local IntervalInput = CreateElement("TextBox", {
            Name = "IntervalInput",
            BackgroundColor3 = THEME.BACKGROUND,
            BackgroundTransparency = 0.3,
            BorderSizePixel = 0,
            Position = UDim2.new(0, 0, 0, 25),
            Size = UDim2.new(0.6, 0, 0, 30),
            Font = Enum.Font.Gotham,
            PlaceholderText = "0.005",
            Text = tostring(settings.Interval),
            TextColor3 = THEME.ON_SURFACE,
            TextSize = 12,
            Parent = IntervalCard
        })

        local IntervalInputCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = IntervalInput
        })

        local ApplyButton = CreateElement("TextButton", {
            Name = "ApplyButton",
            BackgroundColor3 = THEME.PRIMARY,
            BackgroundTransparency = 0.2,
            BorderSizePixel = 0,
            Position = UDim2.new(0.65, 0, 0, 25),
            Size = UDim2.new(0.3, 0, 0, 30),
            Font = Enum.Font.GothamBold,
            Text = "Apply",
            TextColor3 = THEME.ON_SURFACE,
            TextSize = 12,
            Parent = IntervalCard
        })

        local ApplyCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = ApplyButton
        })

        ApplyButton.MouseButton1Click:Connect(function()
            local newInterval = tonumber(IntervalInput.Text)
            if newInterval and newInterval > 0 then
                settings.Interval = newInterval
                IntervalInput.Text = tostring(newInterval)
            else
                IntervalInput.Text = tostring(settings.Interval)
            end
        end)
    end

    UpdateToggle()
end

local function CreateSettingsContent(contentFrame)
    -- UI Settings Card
    local UICard = CreateElement("Frame", {
        Name = "UICard",
        BackgroundColor3 = THEME.SURFACE,
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 120),
        LayoutOrder = 1,
        Parent = contentFrame
    })

    local UICorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = UICard
    })

    local UIPadding = CreateElement("UIPadding", {
        PaddingAll = UDim.new(0, 15),
        Parent = UICard
    })

    local UITitle = CreateElement("TextLabel", {
        Name = "UITitle",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = "🎨 UI Settings",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = UICard
    })

    -- Transparency Slider
    local TransparencyLabel = CreateElement("TextLabel", {
        Name = "TransparencyLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 30),
        Size = UDim2.new(1, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Transparency: " .. math.floor(Settings.Transparency * 100) .. "%",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = UICard
    })

    local TransparencySlider = CreateElement("Frame", {
        Name = "TransparencySlider",
        BackgroundColor3 = THEME.SURFACE_VARIANT,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 55),
        Size = UDim2.new(0.8, 0, 0, 20),
        Parent = UICard
    })

    local SliderCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = TransparencySlider
    })

    local SliderButton = CreateElement("Frame", {
        Name = "SliderButton",
        BackgroundColor3 = THEME.PRIMARY,
        BorderSizePixel = 0,
        Position = UDim2.new(Settings.Transparency, -10, 0.5, -10),
        Size = UDim2.new(0, 20, 0, 20),
        Parent = TransparencySlider
    })

    local SliderButtonCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = SliderButton
    })

    -- Info Card
    local InfoCard = CreateElement("Frame", {
        Name = "InfoCard",
        BackgroundColor3 = THEME.SURFACE,
        BackgroundTransparency = 0.3,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 100),
        LayoutOrder = 2,
        Parent = contentFrame
    })

    local InfoCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = InfoCard
    })

    local InfoPadding = CreateElement("UIPadding", {
        PaddingAll = UDim.new(0, 15),
        Parent = InfoCard
    })

    local InfoTitle = CreateElement("TextLabel", {
        Name = "InfoTitle",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = "ℹ️ Information",
        TextColor3 = THEME.ON_SURFACE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = InfoCard
    })

    local InfoText = CreateElement("TextLabel", {
        Name = "InfoText",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 30),
        Size = UDim2.new(1, 0, 1, -30),
        Font = Enum.Font.Gotham,
        Text = "Script Hub v1.0 - Modular Roblox Automation\nCreated with modular architecture for easy expansion",
        TextColor3 = THEME.SURFACE_VARIANT,
        TextSize = 12,
        TextXAlignment = Enum.TextXAlignment.Left,
        TextYAlignment = Enum.TextYAlignment.Top,
        TextWrapped = true,
        Parent = InfoCard
    })
end

-- Enhanced Main Initialization Function with optimizations
local function InitializeScriptHub()
    print("🚀 Initializing Roblox Script Hub...")
    print("🎨 Current Theme: " .. Settings.CurrentTheme)
    print("⚡ Animations: " .. (Settings.EnableAnimations and "Enabled" or "Disabled"))

    -- Debug: Check if PlayerGui exists
    if not PlayerGui then
        error("PlayerGui not found! Make sure this script runs in a LocalScript.")
    end

    print("📱 PlayerGui found: " .. tostring(PlayerGui))

    -- Create base GUI with enhanced features
    local ScreenGui, MainFrame, TitleBar, CloseButton, MinimizeButton, MenuContainer, ContentArea = CreateBaseGUI()
    print("🖼️ Base GUI created successfully")

    -- Create enhanced tab system
    local MenuButtons, ContentFrames, TabIndicator = CreateTabSystem(MenuContainer, ContentArea)
    print("📑 Tab system created successfully")

    -- Populate content for each tab with enhanced features
    CreateHomeContent(ContentFrames["Home"])
    CreateModuleContent(ContentFrames["AutoClick"], "AutoClick")
    CreateModuleContent(ContentFrames["AutoFarm"], "AutoFarm")
    CreateModuleContent(ContentFrames["Player"], "Player")
    CreateSettingsContent(ContentFrames["Settings"])

    -- Enhanced canvas size management with performance optimization
    for tabName, frame in pairs(ContentFrames) do
        local contentList = frame:FindFirstChild("UIListLayout")
        if contentList then
            local lastSize = 0
            local updateConnection = contentList:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
                local newSize = contentList.AbsoluteContentSize.Y + 50
                if math.abs(newSize - lastSize) > 5 then -- Only update if significant change
                    lastSize = newSize
                    if Settings.EnableAnimations then
                        AnimationUtils:Tween(frame, {
                            CanvasSize = UDim2.new(0, 0, 0, newSize)
                        }, 0.2)
                    else
                        frame.CanvasSize = UDim2.new(0, 0, 0, newSize)
                    end
                end
            end)
            ConnectionManager:Add("CanvasUpdate_" .. tabName, updateConnection)
        end
    end

    -- Auto-save configuration periodically
    spawn(function()
        while ScreenGui.Parent do
            wait(30) -- Save every 30 seconds
            ConfigManager:SaveConfig()
        end
    end)

    -- Performance monitoring (optional)
    if Settings.EnableAnimations then
        spawn(function()
            local lastFPS = 0
            while ScreenGui.Parent do
                wait(5)
                local currentFPS = 1 / RunService.Heartbeat:Wait()
                if currentFPS < 30 and lastFPS >= 30 then
                    print("⚠️ Low FPS detected, consider disabling animations")
                end
                lastFPS = currentFPS
            end
        end)
    end

    -- Success message with enhanced info
    print("✅ Roblox Script Hub initialized successfully!")
    print("📋 Available modules: " .. table.concat({"AutoClick", "AutoFarm", "Player"}, ", "))
    print("🎯 Active tab: " .. Settings.ActiveTab)
    print("💾 Auto-save: " .. (Settings.AutoSave and "Enabled" or "Disabled"))

    -- Save initial configuration
    ConfigManager:SaveConfig()

    return {
        ScreenGui = ScreenGui,
        MainFrame = MainFrame,
        TitleBar = TitleBar,
        MenuButtons = MenuButtons,
        ContentFrames = ContentFrames,
        TabIndicator = TabIndicator,
        ModuleSystem = ModuleSystem,
        ThemeManager = ThemeManager,
        ConfigManager = ConfigManager,
        ConnectionManager = ConnectionManager,
        AnimationUtils = AnimationUtils
    }
end

-- Auto-start the script hub with error handling
local ScriptHub
local success, error = pcall(function()
    ScriptHub = InitializeScriptHub()
end)

if not success then
    warn("❌ Failed to initialize Roblox Script Hub: " .. tostring(error))
    print("🔧 Trying basic initialization...")

    -- Fallback basic initialization
    pcall(function()
        local ScreenGui = CreateElement("ScreenGui", {
            Name = "RobloxScriptHub_Fallback",
            ResetOnSpawn = false
        }, PlayerGui)

        local ErrorFrame = CreateElement("Frame", {
            Name = "ErrorFrame",
            AnchorPoint = Vector2.new(0.5, 0.5),
            BackgroundColor3 = Color3.fromRGB(229, 9, 20),
            Position = UDim2.new(0.5, 0, 0.5, 0),
            Size = UDim2.new(0, 400, 0, 200)
        }, ScreenGui)

        local ErrorCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 12)
        }, ErrorFrame)

        local ErrorText = CreateElement("TextLabel", {
            BackgroundTransparency = 1,
            Size = UDim2.new(1, -40, 1, -40),
            Position = UDim2.new(0, 20, 0, 20),
            Font = Enum.Font.GothamBold,
            Text = "❌ Script Hub Error\n\n" .. tostring(error) .. "\n\nPlease check console for details.",
            TextColor3 = Color3.fromRGB(255, 255, 255),
            TextSize = 14,
            TextWrapped = true,
            TextYAlignment = Enum.TextYAlignment.Top
        }, ErrorFrame)

        print("⚠️ Fallback GUI created. Error: " .. tostring(error))
    end)
else
    print("✅ Script Hub initialized successfully!")
end

-- Enhanced Export for external use with full API access
_G.RobloxScriptHub = {
    -- Core Components
    Hub = ScriptHub,
    ModuleSystem = ModuleSystem,
    ThemeManager = ThemeManager,
    ConfigManager = ConfigManager,
    ConnectionManager = ConnectionManager,
    AnimationUtils = AnimationUtils,

    -- Settings and Themes
    Settings = Settings,
    THEME = THEME,
    THEMES = THEMES,

    -- Utility Functions
    CreateElement = CreateElement,
    CreateCard = CreateCard,
    CreateToggle = CreateToggle,

    -- Module Management
    RegisterModule = function(moduleData)
        ModuleSystem:RegisterModule(moduleData)
        print("📦 Module registered: " .. moduleData.Name)
    end,

    CreateModuleTab = function(moduleName)
        if ScriptHub.ContentFrames[moduleName] then
            CreateModuleContent(ScriptHub.ContentFrames[moduleName], moduleName)
            print("🔧 Module tab created: " .. moduleName)
        end
    end,

    -- Theme Management
    SetTheme = function(themeName)
        ThemeManager:SetTheme(themeName)
        ConfigManager:SaveConfig()
        print("🎨 Theme changed to: " .. themeName)
    end,

    GetAvailableThemes = function()
        local themes = {}
        for name, _ in pairs(THEMES) do
            table.insert(themes, name)
        end
        return themes
    end,

    -- Configuration Management
    SaveConfig = function()
        ConfigManager:SaveConfig()
    end,

    LoadConfig = function()
        ConfigManager:LoadConfig()
    end,

    ResetConfig = function()
        ConfigManager:ResetConfig()
    end,

    ExportConfig = function()
        return ConfigManager:ExportConfig()
    end,

    -- Animation Control
    EnableAnimations = function(enabled)
        Settings.EnableAnimations = enabled
        ConfigManager:SaveConfig()
        print("⚡ Animations " .. (enabled and "enabled" or "disabled"))
    end,

    -- Performance Utilities
    GetPerformanceInfo = function()
        return {
            ActiveModules = #ModuleSystem.ActiveModules,
            CurrentTheme = Settings.CurrentTheme,
            AnimationsEnabled = Settings.EnableAnimations,
            LastSaved = Settings.LastUsed,
            MemoryConnections = 0 -- Could be enhanced to count actual connections
        }
    end,

    -- Advanced Features
    CreateCustomCard = function(parent, title, description, options)
        options = options or {}
        return CreateCard(parent, title, description, options.layoutOrder, options.cardType)
    end,

    AddCustomAnimation = function(object, properties, duration, style, direction)
        return AnimationUtils:Tween(object, properties, duration, style, direction)
    end,

    -- Cleanup
    Destroy = function()
        ConnectionManager:DisconnectAll()
        if ScriptHub.ScreenGui then
            ScriptHub.ScreenGui:Destroy()
        end
        _G.RobloxScriptHub = nil
        print("🗑️ Roblox Script Hub destroyed and cleaned up")
    end
}

print("🌟 Roblox Script Hub API exported to _G.RobloxScriptHub")
print("📚 Use _G.RobloxScriptHub.GetAvailableThemes() to see available themes")
print("🎮 Use _G.RobloxScriptHub.RegisterModule(moduleData) to add custom modules")
