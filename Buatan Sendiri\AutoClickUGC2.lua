local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")

local THEME_RED = Color3.fromRGB(229, 9, 20)
local THEME_BLACK = Color3.fromRGB(20, 20, 20)
local THEME_DARK_GRAY = Color3.fromRGB(35, 35, 35)
local THEME_LIGHT_GRAY = Color3.fromRGB(70, 70, 70)
local THEME_WHITE = Color3.fromRGB(255, 255, 255)
local THEME_GRAY_BLUE = Color3.fromRGB(30, 35, 45)

local Settings = {
    AutoClick = false,
    ClickInterval = 0.005, -- 0.005 seconds for ultra-fast clicking
    Transparency = 0.2,
    DragSpeed = 0.05,
    MinimizedState = false,
    ActiveTab = "Home",
    UISize = UDim2.new(0, 500, 0, 300)
}

local function CreateElement(className, properties)
    local element = Instance.new(className)
    for property, value in pairs(properties) do
        element[property] = value
    end
    return element
end

local function CreateGUI()
    local ScreenGui = CreateElement("ScreenGui", {
        Name = "AutoClickUGC2GUI",
        ResetOnSpawn = false,
        ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
        Parent = PlayerGui
    })

    local MainFrame = CreateElement("Frame", {
        Name = "MainFrame",
        AnchorPoint = Vector2.new(0.5, 0.5),
        BackgroundColor3 = THEME_BLACK,
        BackgroundTransparency = Settings.Transparency,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, 0, 0.5, 0),
        Size = Settings.UISize,
        Parent = ScreenGui
    })

    local UICorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = MainFrame
    })

    local TitleBar = CreateElement("Frame", {
        Name = "TitleBar",
        BackgroundColor3 = THEME_RED,
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 36),
        Parent = MainFrame
    })

    local TitleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 10),
        Parent = TitleBar
    })

    local TitleCornerFix = CreateElement("Frame", {
        Name = "TitleCornerFix",
        BackgroundColor3 = THEME_RED,
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0.5, 0),
        Size = UDim2.new(1, 0, 0.5, 0),
        Parent = TitleBar
    })

    local TitleText = CreateElement("TextLabel", {
        Name = "TitleText",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 12, 0, 0),
        Size = UDim2.new(1, -24, 1, 0),
        Font = Enum.Font.GothamBold,
        Text = "Auto Click UGC2",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = TitleBar
    })

    local CloseButton = CreateElement("TextButton", {
        Name = "CloseButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -8, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "X",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        Parent = TitleBar
    })

    local MinimizeButton = CreateElement("TextButton", {
        Name = "MinimizeButton",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundTransparency = 1,
        Position = UDim2.new(1, -40, 0.5, 0),
        Size = UDim2.new(0, 24, 0, 24),
        Font = Enum.Font.GothamBold,
        Text = "-",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        Parent = TitleBar
    })

    local MainLayout = CreateElement("Frame", {
        Name = "MainLayout",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 36),
        Size = UDim2.new(1, 0, 1, -36),
        Parent = MainFrame
    })

    local MenuContainer = CreateElement("Frame", {
        Name = "MenuContainer",
        BackgroundTransparency = 1,
        Size = UDim2.new(0, 120, 1, 0),
        Parent = MainLayout
    })

    local MenuPadding = CreateElement("UIPadding", {
        PaddingTop = UDim.new(0, 10),
        Parent = MenuContainer
    })

    local MenuList = CreateElement("UIListLayout", {
        Padding = UDim.new(0, 8),
        HorizontalAlignment = Enum.HorizontalAlignment.Center,
        SortOrder = Enum.SortOrder.LayoutOrder,
        Parent = MenuContainer
    })

    local ContentArea = CreateElement("Frame", {
        Name = "ContentArea",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 120, 0, 0),
        Size = UDim2.new(1, -120, 1, 0),
        Parent = MainLayout
    })

    local MenuItems = {
        {Name = "Home", Icon = "🏠", Order = 1},
        {Name = "Settings", Icon = "⚙️", Order = 2}
    }

    local MenuButtons = {}
    local ContentFrames = {}

    for _, item in ipairs(MenuItems) do
        local MenuButton = CreateElement("TextButton", {
            Name = item.Name .. "Button",
            BackgroundColor3 = Settings.ActiveTab == item.Name and THEME_RED or THEME_GRAY_BLUE,
            BackgroundTransparency = Settings.ActiveTab == item.Name and 0.1 or 0.5,
            BorderSizePixel = 0,
            Size = UDim2.new(0.9, 0, 0, 36),
            Font = Enum.Font.GothamSemibold,
            Text = item.Icon .. " " .. item.Name,
            TextColor3 = THEME_WHITE,
            TextSize = 14,
            LayoutOrder = item.Order,
            Parent = MenuContainer
        })

        local ButtonCorner = CreateElement("UICorner", {
            CornerRadius = UDim.new(0, 6),
            Parent = MenuButton
        })

        local ContentFrame = CreateElement("ScrollingFrame", {
            Name = item.Name .. "Content",
            BackgroundTransparency = 1,
            BorderSizePixel = 0,
            Size = UDim2.new(1, 0, 1, 0),
            CanvasSize = UDim2.new(0, 0, 0, 0),
            ScrollBarThickness = 4,
            ScrollingDirection = Enum.ScrollingDirection.Y,
            VerticalScrollBarInset = Enum.ScrollBarInset.Always,
            Visible = Settings.ActiveTab == item.Name,
            Parent = ContentArea
        })

        local ContentPadding = CreateElement("UIPadding", {
            PaddingLeft = UDim.new(0, 10),
            PaddingRight = UDim.new(0, 10),
            PaddingTop = UDim.new(0, 10),
            PaddingBottom = UDim.new(0, 10),
            Parent = ContentFrame
        })

        local ContentList = CreateElement("UIListLayout", {
            Padding = UDim.new(0, 10),
            SortOrder = Enum.SortOrder.LayoutOrder,
            Parent = ContentFrame
        })

        MenuButtons[item.Name] = MenuButton
        ContentFrames[item.Name] = ContentFrame

        MenuButton.MouseButton1Click:Connect(function()
            Settings.ActiveTab = item.Name

            for name, button in pairs(MenuButtons) do
                TweenService:Create(button, TweenInfo.new(0.2), {
                    BackgroundColor3 = name == item.Name and THEME_RED or THEME_GRAY_BLUE,
                    BackgroundTransparency = name == item.Name and 0.1 or 0.5
                }):Play()
            end

            for name, frame in pairs(ContentFrames) do
                frame.Visible = name == item.Name
            end
        end)
    end

    local HomeContent = ContentFrames["Home"]

    -- Auto Click Card
    local ClickCard = CreateElement("Frame", {
        Name = "ClickCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 80),
        Parent = HomeContent
    })

    local ClickCardCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = ClickCard
    })

    local ClickTitle = CreateElement("TextLabel", {
        Name = "ClickTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 10),
        Size = UDim2.new(1, -20, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Auto Click",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = ClickCard
    })

    local ClickStatus = CreateElement("TextLabel", {
        Name = "ClickStatus",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 35),
        Size = UDim2.new(0.6, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Status: Idle",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = ClickCard
    })

    local ClickToggle = CreateElement("Frame", {
        Name = "ClickToggle",
        AnchorPoint = Vector2.new(1, 0.5),
        BackgroundColor3 = THEME_LIGHT_GRAY,
        BorderSizePixel = 0,
        Position = UDim2.new(1, -10, 0.5, 0),
        Size = UDim2.new(0, 44, 0, 24),
        Parent = ClickCard
    })

    local ClickToggleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = ClickToggle
    })

    local ClickToggleCircle = CreateElement("Frame", {
        Name = "ClickToggleCircle",
        AnchorPoint = Vector2.new(0, 0.5),
        BackgroundColor3 = THEME_WHITE,
        Position = UDim2.new(0, 4, 0.5, 0),
        Size = UDim2.new(0, 16, 0, 16),
        Parent = ClickToggle
    })

    local ClickToggleCircleCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(1, 0),
        Parent = ClickToggleCircle
    })

    -- Dragging functionality
    local Dragging = false
    local DragInput = nil
    local DragStart = nil
    local StartPos = nil

    local function UpdateDrag()
        if Dragging and DragInput then
            local Delta = DragInput.Position - DragStart
            local Position = UDim2.new(StartPos.X.Scale, StartPos.X.Offset + Delta.X, StartPos.Y.Scale, StartPos.Y.Offset + Delta.Y)

            -- Use TweenService for smoother dragging
            TweenService:Create(MainFrame, TweenInfo.new(Settings.DragSpeed), {Position = Position}):Play()
        end
    end

    TitleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
            Dragging = true
            DragStart = input.Position
            StartPos = MainFrame.Position

            -- Continue dragging even if mouse leaves the GUI
            local Connection
            Connection = RunService.RenderStepped:Connect(function()
                if not UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton1) and
                   not UserInputService:IsMouseButtonPressed(Enum.UserInputType.Touch) then
                    Dragging = false
                    Connection:Disconnect()
                end
            end)
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseMovement or input.UserInputType == Enum.UserInputType.Touch then
            DragInput = input
            if Dragging then
                UpdateDrag()
            end
        end
    end)

    CloseButton.MouseButton1Click:Connect(function()
        ScreenGui:Destroy()
        Settings.AutoClick = false
    end)

    MinimizeButton.MouseButton1Click:Connect(function()
        Settings.MinimizedState = not Settings.MinimizedState

        if Settings.MinimizedState then
            -- Minimize
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = UDim2.new(0, 500, 0, 36)
            }):Play()
            MainLayout.Visible = false
        else
            -- Restore
            TweenService:Create(MainFrame, TweenInfo.new(0.3), {
                Size = Settings.UISize
            }):Play()
            MainLayout.Visible = true
        end
    end)

    local function UpdateClickToggle()
        local TogglePos = Settings.AutoClick and UDim2.new(1, -20, 0.5, 0) or UDim2.new(0, 4, 0.5, 0)
        local ToggleColor = Settings.AutoClick and THEME_RED or THEME_LIGHT_GRAY

        TweenService:Create(ClickToggleCircle, TweenInfo.new(0.2), {Position = TogglePos}):Play()
        TweenService:Create(ClickToggle, TweenInfo.new(0.2), {BackgroundColor3 = ToggleColor}):Play()
        ClickStatus.Text = "Status: " .. (Settings.AutoClick and "Running" or "Idle")
    end

    -- Connect toggle button
    ClickToggle.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            Settings.AutoClick = not Settings.AutoClick
            UpdateClickToggle()
        end
    end)

    return {
        ScreenGui = ScreenGui,
        ContentFrames = ContentFrames,
        ClickStatus = ClickStatus,
        ClickToggle = ClickToggle,
        ClickToggleCircle = ClickToggleCircle
    }
end

local function StartAutoClick()
    local GUI = CreateGUI()
    local SettingsContent = GUI.ContentFrames["Settings"]

    -- Create Settings Tab Content
    -- Click Interval Setting
    local ClickIntervalCard = CreateElement("Frame", {
        Name = "ClickIntervalCard",
        BackgroundColor3 = THEME_DARK_GRAY,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 80),
        Parent = SettingsContent
    })

    local ClickIntervalCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 8),
        Parent = ClickIntervalCard
    })

    local ClickIntervalTitle = CreateElement("TextLabel", {
        Name = "ClickIntervalTitle",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 10),
        Size = UDim2.new(1, -20, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "Click Interval (seconds)",
        TextColor3 = THEME_WHITE,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = ClickIntervalCard
    })

    local ClickIntervalInput = CreateElement("TextBox", {
        Name = "ClickIntervalInput",
        BackgroundColor3 = THEME_BLACK,
        BackgroundTransparency = 0.5,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 10, 0, 40),
        Size = UDim2.new(0.5, -20, 0, 30),
        Font = Enum.Font.Gotham,
        PlaceholderText = "Enter interval (e.g. 0.005)",
        Text = tostring(Settings.ClickInterval),
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        Parent = ClickIntervalCard
    })

    local ClickIntervalInputCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 6),
        Parent = ClickIntervalInput
    })

    local ClickIntervalApply = CreateElement("TextButton", {
        Name = "ClickIntervalApply",
        BackgroundColor3 = THEME_RED,
        BackgroundTransparency = 0.2,
        BorderSizePixel = 0,
        Position = UDim2.new(0.55, 0, 0, 40),
        Size = UDim2.new(0.4, 0, 0, 30),
        Font = Enum.Font.GothamBold,
        Text = "Apply",
        TextColor3 = THEME_WHITE,
        TextSize = 14,
        Parent = ClickIntervalCard
    })

    local ClickIntervalApplyCorner = CreateElement("UICorner", {
        CornerRadius = UDim.new(0, 6),
        Parent = ClickIntervalApply
    })

    ClickIntervalApply.MouseButton1Click:Connect(function()
        local newInterval = tonumber(ClickIntervalInput.Text)
        if newInterval and newInterval > 0 then
            Settings.ClickInterval = newInterval
            ClickIntervalInput.Text = tostring(newInterval)
        else
            ClickIntervalInput.Text = tostring(Settings.ClickInterval)
        end
    end)

    -- Ultra-fast Auto Click loop
    spawn(function()
        -- Get the Click remote from ReplicatedStorage
        local Click = ReplicatedStorage.Remotes.Click -- RemoteEvent

        while true do
            if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
                break
            end

            if Settings.AutoClick then
                -- Fire click event as fast as possible with minimal delay
                Click:FireServer()

                -- Tiny wait to prevent client freeze and allow other processes to run
                wait(Settings.ClickInterval) -- Ultra-fast interval (default 0.005 seconds)
            else
                -- When not active, just check occasionally
                wait(0.005) -- Still check frequently
            end
        end
    end)

    -- Main loop for status updates only
    spawn(function()
        while wait(0.005) do -- Use faster loop interval
            if not GUI.ScreenGui or not GUI.ScreenGui.Parent then
                break
            end

            -- Status updates only - actual functionality is in separate loop
            if Settings.AutoClick then
                GUI.ClickStatus.Text = "Status: Clicking..."
            else
                GUI.ClickStatus.Text = "Status: Idle"
            end
        end
    end)

    return GUI
end

-- Start the auto click
StartAutoClick()
