# Dead Shelter Script

Script khusus untuk game Dead Shelter di Roblox dengan fitur ESP dan teleportasi.

## Fitur Utama

### 🎯 Item ESP
- **Lokasi**: `workspace.DraggableFolder`
- **Deteksi Otomatis**: Script akan otomatis mendeteksi semua item seperti Bandage, Big Plank, dll.
- **<PERSON>a Otomatis**: Mengambil nama item secara otomatis dari properti `.Name`
- **Jarak**: Menampilkan jarak ke item dalam meter
- **Warna**: Hijau untuk item

### 👹 Enemy ESP  
- **Lokasi**: `workspace.Enemies`
- **Deteksi Otomatis**: Script akan otomatis mendeteksi semua enemy
- **<PERSON><PERSON> Otomatis**: Mengambil nama enemy secara otomatis
- **Jarak**: Menampilkan jarak ke enemy dalam meter
- **Warna**: Merah untuk enemy

### 🚀 Teleportasi
- **Base**: Teleport ke `workspace.Baseplate`
- **Pawnshop**: Teleport ke `workspace["Noob's Pawnshop"]`
- **Offset Aman**: Teleport 5 studs di atas target untuk keamanan

### 👻 Character Manipulation
- **Invisibility**: Toggle karakter invisible/visible
- **God Mode**: Infinite health dan anti-damage
- **Auto-Reapply**: Fitur otomatis aktif kembali setelah respawn

## Cara Menggunakan

### 1. Menjalankan Script
```lua
-- Copy dan paste script DeadShelterScript.lua ke executor
-- Script akan otomatis membuat GUI dan mulai berjalan
```

### 2. Kontrol GUI
- **Drag**: Klik dan drag title bar untuk memindahkan GUI
- **Minimize**: Klik tombol "-" untuk minimize/restore
- **Close**: Klik tombol "×" untuk menutup script

### 3. ESP Controls
- **📦 Item ESP**: Toggle on/off ESP untuk item (otomatis scan)
- **👹 Enemy ESP**: Toggle on/off ESP untuk enemy (otomatis scan)

### 4. Teleport Controls
- **🏠 Teleport to Base**: Teleport ke base (Baseplate)
- **🏪 Teleport to Pawnshop**: Teleport ke pawnshop

### 5. Character Controls
- **👻 Invisibility**: Toggle karakter invisible/visible
- **🛡️ God Mode**: Toggle infinite health dan anti-damage

## Konfigurasi

### ESP Settings
```lua
-- Jarak maksimal ESP (dalam studs)
ESP_CONFIG.Items.MaxDistance = 5000    -- Item ESP
ESP_CONFIG.Enemies.MaxDistance = 10000 -- Enemy ESP

-- Warna ESP
ESP_CONFIG.Items.TextColor = Color3.fromRGB(0, 255, 0)    -- Hijau
ESP_CONFIG.Enemies.TextColor = Color3.fromRGB(229, 9, 20) -- Merah
```

### GUI Settings
```lua
-- Ukuran GUI
Settings.UISize = UDim2.new(0, 400, 0, 350)

-- Transparansi
Settings.Transparency = 0.2

-- Kecepatan drag
Settings.DragSpeed = 0.05
```

## Fitur Otomatis

### Auto-Detection
- Script otomatis mendeteksi item baru yang ditambahkan ke `DraggableFolder`
- Script otomatis mendeteksi enemy baru yang ditambahkan ke `Enemies`
- ESP otomatis dibuat untuk object baru
- ESP otomatis dihapus ketika object dihapus

### Character Features
- Invisibility dan God Mode otomatis aktif kembali setelah respawn
- God Mode menggunakan infinite health dan anti-damage protection
- Invisibility mempengaruhi semua body parts dan accessories

### Real-time Updates
- Jarak ke item/enemy diupdate secara real-time
- ESP visibility berdasarkan jarak maksimal
- Status counter diupdate otomatis

## Troubleshooting

### ESP Tidak Muncul
1. Pastikan folder `DraggableFolder` dan `Enemies` ada di workspace
2. Klik tombol "Scan Items" atau "Scan Enemies"
3. Toggle ESP on jika masih off

### Teleport Tidak Berfungsi
1. Pastikan `Baseplate` ada di workspace untuk teleport base
2. Pastikan `Noob's Pawnshop` ada di workspace untuk teleport pawnshop
3. Pastikan character sudah spawn

### GUI Tidak Muncul
1. Pastikan script dijalankan dengan benar
2. Check console untuk error messages
3. Restart script jika diperlukan

## Console Output

Script akan memberikan informasi di console:
```
Dead Shelter Script initialized!
Performing initial scan...
Item ESP created for: Bandage
Item ESP created for: Big Plank
Found 2 new items!
Enemy ESP created for: Zombie
Found 1 new enemies!
=== Dead Shelter Script Ready ===
```

## Keamanan

- Script menggunakan offset teleport untuk keamanan
- ESP hanya visual, tidak mempengaruhi gameplay
- Tidak ada auto-collect atau cheat lainnya
- Script dapat dihentikan kapan saja dengan tombol close

## Kompatibilitas

- ✅ Synapse X
- ✅ Krnl  
- ✅ Script-Ware
- ✅ Executor lainnya yang support Roblox API

---

**Catatan**: Script ini dibuat khusus untuk Dead Shelter. Pastikan nama folder dan object sesuai dengan game yang dimainkan.
